#!/usr/bin/env ts-node

import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

interface PageInfo {
  path: string
  name: string
  testPath: string
  hasApi?: boolean
  apiService?: string
  isSimple?: boolean
}

const pages: PageInfo[] = [
  // Auth pages
  { path: 'register', name: 'RegisterPage', testPath: 'register/__tests__/page.test.tsx', hasApi: true, apiService: 'authApi' },

  // Financial pages
  { path: 'customers', name: 'CustomersPage', testPath: 'customers/__tests__/page.test.tsx', hasApi: true, apiService: 'customersApi' },
  { path: 'vendors', name: 'VendorsPage', testPath: 'vendors/__tests__/page.test.tsx', hasApi: true, apiService: 'vendorsApi' },
  { path: 'bills', name: 'BillsPage', testPath: 'bills/__tests__/page.test.tsx', hasApi: true, apiService: 'billsApi' },
  { path: 'assets', name: 'AssetsPage', testPath: 'assets/__tests__/page.test.tsx', hasApi: true, apiService: 'assetsApi' },
  { path: 'banking', name: 'BankingPage', testPath: 'banking/__tests__/page.test.tsx', hasApi: true, apiService: 'bankingApi' },
  { path: 'chart-of-accounts', name: 'ChartOfAccountsPage', testPath: 'chart-of-accounts/__tests__/page.test.tsx', hasApi: true, apiService: 'chartOfAccountsApi' },
  { path: 'payroll', name: 'PayrollPage', testPath: 'payroll/__tests__/page.test.tsx', hasApi: true, apiService: 'payrollApi' },
  { path: 'employees', name: 'EmployeesPage', testPath: 'employees/__tests__/page.test.tsx', hasApi: true, apiService: 'employeesApi' },
  { path: 'inventory', name: 'InventoryPage', testPath: 'inventory/__tests__/page.test.tsx', hasApi: true, apiService: 'inventoryApi' },
  { path: 'expenses', name: 'ExpensesPage', testPath: 'expenses/__tests__/page.test.tsx', hasApi: true, apiService: 'expensesApi' },
  { path: 'taxes', name: 'TaxesPage', testPath: 'taxes/__tests__/page.test.tsx', hasApi: true, apiService: 'taxApi' },
  { path: 'cashflow', name: 'CashflowPage', testPath: 'cashflow/__tests__/page.test.tsx', hasApi: true, apiService: 'cashflowApi' },
  { path: 'budget', name: 'BudgetPage', testPath: 'budget/__tests__/page.test.tsx', hasApi: true, apiService: 'budgetApi' },
  { path: 'journal-entries', name: 'JournalEntriesPage', testPath: 'journal-entries/__tests__/page.test.tsx', hasApi: true, apiService: 'journalEntriesApi' },

  // Reports
  { path: 'reports', name: 'ReportsPage', testPath: 'reports/__tests__/page.test.tsx', hasApi: true, apiService: 'reportsApi' },

  // Management pages
  { path: 'organizations', name: 'OrganizationsPage', testPath: 'organizations/__tests__/page.test.tsx', hasApi: true, apiService: 'organizationsApi' },
  { path: 'branches', name: 'BranchesPage', testPath: 'branches/__tests__/page.test.tsx', hasApi: true, apiService: 'organizationsApi' },
  { path: 'users', name: 'UsersPage', testPath: 'users/__tests__/page.test.tsx', hasApi: true, apiService: 'usersApi' },

  // Other pages
  { path: 'integrations', name: 'IntegrationsPage', testPath: 'integrations/__tests__/page.test.tsx', hasApi: true, apiService: 'integrationsApi' },
  { path: 'profile', name: 'ProfilePage', testPath: 'profile/__tests__/page.test.tsx', hasApi: true, apiService: 'profileApi' },
  { path: 'sales', name: 'SalesPage', testPath: 'sales/__tests__/page.test.tsx', hasApi: true, apiService: 'ordersApi' },
  { path: 'finances', name: 'FinancesPage', testPath: 'finances/__tests__/page.test.tsx', hasApi: false, isSimple: true },

  // Simple/static pages
  { path: 'help', name: 'HelpPage', testPath: 'help/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'pricing', name: 'PricingPage', testPath: 'pricing/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'privacy', name: 'PrivacyPage', testPath: 'privacy/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'terms', name: 'TermsPage', testPath: 'terms/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'not-done', name: 'NotDonePage', testPath: 'not-done/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'custom-not-done', name: 'CustomNotDonePage', testPath: 'custom-not-done/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'utility-not-done', name: 'UtilityNotDonePage', testPath: 'utility-not-done/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'example-feature', name: 'ExampleFeaturePage', testPath: 'example-feature/__tests__/page.test.tsx', hasApi: false, isSimple: true },
  { path: 'error', name: 'ErrorPage', testPath: 'error/__tests__/page.test.tsx', hasApi: false, isSimple: true },
]

function generateTestTemplate(pageInfo: PageInfo): string {
  const { name, hasApi, apiService, isSimple } = pageInfo

  if (isSimple) {
    return `import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ${name} from '../page'

describe('${name}', () => {
  it('renders page content', () => {
    render(<${name} />)

    // Check for main content
    expect(screen.getByRole('main') || document.body).toBeInTheDocument()
  })

  it('renders page title', () => {
    render(<${name} />)

    // Look for title or heading
    const title = screen.queryByRole('heading') || screen.queryByText(/title/i)
    if (title) {
      expect(title).toBeInTheDocument()
    }
  })

  it('renders navigation elements', () => {
    render(<${name} />)

    // Check for navigation links
    const links = screen.queryAllByRole('link')
    expect(links.length).toBeGreaterThanOrEqual(0)
  })

  it('has proper accessibility attributes', () => {
    render(<${name} />)

    // Check for basic accessibility
    const main = screen.queryByRole('main')
    if (main) {
      expect(main).toBeInTheDocument()
    }
  })
})
`
  }

  return `import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import ${name} from '../page'

// Mock Redux API hooks
${hasApi ? `vi.mock('@/redux/services/${apiService}', () => ({
  useGet${name.replace('Page', '')}Query: vi.fn(),
  useCreate${name.replace('Page', '')}Mutation: vi.fn(),
  useUpdate${name.replace('Page', '')}Mutation: vi.fn(),
  useDelete${name.replace('Page', '')}Mutation: vi.fn(),
}))` : ''}

// Mock custom hooks
vi.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    t: (key: string) => key,
    formatCurrency: (amount: number) => \`$\${amount.toFixed(2)}\`,
    formatDate: (date: Date) => date.toLocaleDateString(),
  }),
}))

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleApiError: vi.fn(),
  }),
}))

// Mock components
vi.mock('@/components/common/StateManager', () => ({
  default: ({ children, isLoading, error, data }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error occurred</div>
    if (!data) return <div data-testid="no-data">No data</div>
    return <div data-testid="state-manager">{children}</div>
  },
}))

describe('${name}', () => {
  ${hasApi ? `const mockUseGetQuery = vi.fn()
  const mockUseCreateMutation = vi.fn()
  const mockUseUpdateMutation = vi.fn()
  const mockUseDeleteMutation = vi.fn()

  const mockData = [
    {
      id: '1',
      name: 'Test Item 1',
      createdAt: new Date(),
    },
    {
      id: '2',
      name: 'Test Item 2',
      createdAt: new Date(),
    },
  ]` : ''}

  beforeEach(() => {
    vi.clearAllMocks()

    ${hasApi ? `mockUseGetQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockUseCreateMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])

    mockUseUpdateMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])

    mockUseDeleteMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])` : ''}
  })

  it('renders page title', () => {
    render(<${name} />)

    const title = screen.queryByRole('heading') || screen.queryByText(/title/i)
    if (title) {
      expect(title).toBeInTheDocument()
    }
  })

  ${hasApi ? `it('shows loading state when data is loading', () => {
    mockUseGetQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      refetch: vi.fn(),
    })

    render(<${name} />)
    expect(screen.getByTestId('loading')).toBeInTheDocument()
  })

  it('shows error state when there is an error', () => {
    mockUseGetQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Failed to load data' },
      refetch: vi.fn(),
    })

    render(<${name} />)
    expect(screen.getByTestId('error')).toBeInTheDocument()
  })

  it('renders data when available', () => {
    render(<${name} />)

    expect(screen.getByTestId('state-manager')).toBeInTheDocument()
  })` : `it('renders page content', () => {
    render(<${name} />)

    expect(screen.getByRole('main') || document.body).toBeInTheDocument()
  })`}

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    render(<${name} />)

    // Look for interactive elements
    const buttons = screen.queryAllByRole('button')
    if (buttons.length > 0) {
      await user.click(buttons[0])
    }
  })

  it('has proper accessibility attributes', () => {
    render(<${name} />)

    const main = screen.queryByRole('main')
    if (main) {
      expect(main).toBeInTheDocument()
    }
  })
})
`
}

function ensureDirectoryExists(filePath: string) {
  const dir = path.dirname(filePath)
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
}

function generateTests() {
  const baseDir = path.join(__dirname, '../src/app/[locale]')

  pages.forEach(pageInfo => {
    const testFilePath = path.join(baseDir, pageInfo.testPath)

    // Skip if test file already exists
    if (fs.existsSync(testFilePath)) {
      console.log(`Skipping ${pageInfo.testPath} - already exists`)
      return
    }

    ensureDirectoryExists(testFilePath)

    const testContent = generateTestTemplate(pageInfo)
    fs.writeFileSync(testFilePath, testContent)

    console.log(`Generated test file: ${pageInfo.testPath}`)
  })

  console.log('\\nTest generation complete!')
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTests()
}

export { generateTests, pages }
