#!/usr/bin/env ts-node

import { execSync } from 'child_process'
import * as fs from 'fs'
import * as path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

interface TestResult {
  file: string
  status: 'pass' | 'fail' | 'skip'
  duration?: number
  error?: string
}

interface TestSummary {
  total: number
  passed: number
  failed: number
  skipped: number
  results: TestResult[]
}

function findTestFiles(dir: string): string[] {
  const testFiles: string[] = []

  function searchDirectory(currentDir: string) {
    const items = fs.readdirSync(currentDir)

    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory() && item === '__tests__') {
        // Found a __tests__ directory, look for test files
        const testDir = fullPath
        const testDirItems = fs.readdirSync(testDir)

        for (const testFile of testDirItems) {
          if (testFile.endsWith('.test.tsx') || testFile.endsWith('.test.ts')) {
            testFiles.push(path.join(testDir, testFile))
          }
        }
      } else if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        // Recursively search subdirectories
        searchDirectory(fullPath)
      }
    }
  }

  searchDirectory(dir)
  return testFiles
}

function runSingleTest(testFile: string): TestResult {
  const relativePath = path.relative(process.cwd(), testFile)

  try {
    console.log(`Running: ${relativePath}`)
    const startTime = Date.now()

    execSync(`npx vitest run "${testFile}" --reporter=verbose`, {
      stdio: 'pipe',
      encoding: 'utf8'
    })

    const duration = Date.now() - startTime
    console.log(`✅ PASS: ${relativePath} (${duration}ms)`)

    return {
      file: relativePath,
      status: 'pass',
      duration
    }
  } catch (error: any) {
    console.log(`❌ FAIL: ${relativePath}`)
    console.log(`Error: ${error.message}`)

    return {
      file: relativePath,
      status: 'fail',
      error: error.message
    }
  }
}

function runAllTests(testFiles: string[]): TestSummary {
  const results: TestResult[] = []
  let passed = 0
  let failed = 0
  let skipped = 0

  console.log(`\n🧪 Running ${testFiles.length} test files...\n`)

  for (const testFile of testFiles) {
    const result = runSingleTest(testFile)
    results.push(result)

    switch (result.status) {
      case 'pass':
        passed++
        break
      case 'fail':
        failed++
        break
      case 'skip':
        skipped++
        break
    }
  }

  return {
    total: testFiles.length,
    passed,
    failed,
    skipped,
    results
  }
}

function printSummary(summary: TestSummary) {
  console.log('\n' + '='.repeat(60))
  console.log('📊 TEST SUMMARY')
  console.log('='.repeat(60))
  console.log(`Total Tests: ${summary.total}`)
  console.log(`✅ Passed: ${summary.passed}`)
  console.log(`❌ Failed: ${summary.failed}`)
  console.log(`⏭️  Skipped: ${summary.skipped}`)
  console.log(`📈 Success Rate: ${((summary.passed / summary.total) * 100).toFixed(1)}%`)

  if (summary.failed > 0) {
    console.log('\n❌ FAILED TESTS:')
    summary.results
      .filter(r => r.status === 'fail')
      .forEach(result => {
        console.log(`  - ${result.file}`)
        if (result.error) {
          console.log(`    Error: ${result.error.split('\n')[0]}`)
        }
      })
  }

  console.log('\n' + '='.repeat(60))
}

function generateTestReport(summary: TestSummary) {
  const reportPath = path.join(process.cwd(), 'test-results.json')

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: summary.total,
      passed: summary.passed,
      failed: summary.failed,
      skipped: summary.skipped,
      successRate: ((summary.passed / summary.total) * 100).toFixed(1) + '%'
    },
    results: summary.results
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log(`📄 Test report saved to: ${reportPath}`)
}

function main() {
  const args = process.argv.slice(2)
  const pageTestsDir = path.join(process.cwd(), 'src/app/[locale]')

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: npm run test:pages [options]

Options:
  --help, -h     Show this help message
  --watch, -w    Run tests in watch mode
  --coverage     Run tests with coverage
  --file <path>  Run specific test file
  --report       Generate detailed test report

Examples:
  npm run test:pages
  npm run test:pages -- --coverage
  npm run test:pages -- --file src/app/[locale]/dashboard/__tests__/page.test.tsx
`)
    return
  }

  if (args.includes('--file')) {
    const fileIndex = args.indexOf('--file')
    const testFile = args[fileIndex + 1]

    if (!testFile) {
      console.error('❌ Error: --file option requires a file path')
      process.exit(1)
    }

    if (!fs.existsSync(testFile)) {
      console.error(`❌ Error: Test file not found: ${testFile}`)
      process.exit(1)
    }

    const result = runSingleTest(testFile)
    console.log(`\n📊 Result: ${result.status.toUpperCase()}`)
    return
  }

  if (args.includes('--watch') || args.includes('-w')) {
    console.log('🔄 Running tests in watch mode...')
    execSync('npx vitest --watch src/app/\\[locale\\]', { stdio: 'inherit' })
    return
  }

  if (args.includes('--coverage')) {
    console.log('📊 Running tests with coverage...')
    execSync('npx vitest run --coverage src/app/\\[locale\\]', { stdio: 'inherit' })
    return
  }

  // Find all test files
  const testFiles = findTestFiles(pageTestsDir)

  if (testFiles.length === 0) {
    console.log('⚠️  No test files found in src/app/[locale]')
    console.log('💡 Run the test generator first: npm run generate:tests')
    return
  }

  console.log(`🔍 Found ${testFiles.length} test files`)

  // Run all tests
  const summary = runAllTests(testFiles)

  // Print summary
  printSummary(summary)

  // Generate report if requested
  if (args.includes('--report')) {
    generateTestReport(summary)
  }

  // Exit with error code if tests failed
  if (summary.failed > 0) {
    process.exit(1)
  }
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { findTestFiles, runSingleTest, runAllTests }
