// API Configuration for routing between Next.js and Go backend

// Environment variables
const GO_BACKEND_URL = process.env.NEXT_PUBLIC_GO_BACKEND_URL || 'http://localhost:8050';
const NEXTJS_API_URL = process.env.NEXT_PUBLIC_NEXTJS_API_URL || '/api';

// Endpoints that should be handled by Next.js
const NEXTJS_ENDPOINTS = [
  'auth',
  'webhooks/stripe',
  'setup-stripe', 
  'subscriptions/checkout',
  'subscriptions/portal',
  'upload',
] as const;

// Endpoints that should be handled by Go backend
const GO_BACKEND_ENDPOINTS = [
  // Core business logic
  'organizations',
  'merchants', 
  'users',
  'profile',
  
  // Financial
  'invoices',
  'bills',
  'expenses',
  'customers',
  'vendors',
  
  // Accounting
  'coa',
  'journal-entries',
  'taxes',
  
  // Banking
  'banking',
  
  // Assets & Inventory
  'assets',
  'inventory',
  
  // Payroll
  'payroll',
  'employees',
  
  // Budget & Cash Flow
  'budget',
  'cashflow',
  
  // Collections & Credit
  'credit-notes',
  'customer-credits',
  'payment-reminders',
  'customer-statements',
  'collection-activities',
  'collection-cases',
  'collection-metrics',
  'collection-templates',
  
  // Templates & Recurring
  'invoice-templates',
  'recurring-invoices',
  'budget-templates',
  
  // Email & Communications
  'emails',
  
  // Reports & Analytics
  'reports',
  'dashboard',
  
  // Settings & Configuration
  'settings',
  
  // Integrations
  'integrations',
  
  // Audit & Monitoring
  'audit-logs',
  'internal',
  
  // Orders
  'orders',
  
  // Health checks
  'health',
] as const;

type NextJSEndpoint = typeof NEXTJS_ENDPOINTS[number];
type GoBackendEndpoint = typeof GO_BACKEND_ENDPOINTS[number];

/**
 * Determines if an endpoint should be handled by Next.js
 */
export function isNextJSEndpoint(endpoint: string): boolean {
  return NEXTJS_ENDPOINTS.some(nextjsEndpoint => 
    endpoint.startsWith(nextjsEndpoint)
  );
}

/**
 * Determines if an endpoint should be handled by Go backend
 */
export function isGoBackendEndpoint(endpoint: string): boolean {
  return GO_BACKEND_ENDPOINTS.some(goEndpoint => 
    endpoint.startsWith(goEndpoint)
  );
}

/**
 * Gets the appropriate base URL for an API endpoint
 */
export function getApiBaseUrl(endpoint: string): string {
  if (isNextJSEndpoint(endpoint)) {
    return NEXTJS_API_URL;
  }
  
  if (isGoBackendEndpoint(endpoint)) {
    return `${GO_BACKEND_URL}/api`;
  }
  
  // Default to Go backend for unknown endpoints
  console.warn(`Unknown endpoint: ${endpoint}, defaulting to Go backend`);
  return `${GO_BACKEND_URL}/api`;
}

/**
 * Builds a complete API URL for an endpoint
 */
export function buildApiUrl(endpoint: string): string {
  const baseUrl = getApiBaseUrl(endpoint);
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseUrl}/${cleanEndpoint}`;
}

/**
 * Creates a fetch base query function for RTK Query that automatically routes to the correct backend
 */
export function createRoutedBaseQuery() {
  return (args: any, api: any, extraOptions: any) => {
    // Extract the URL from args
    let url: string;
    if (typeof args === 'string') {
      url = args;
    } else if (args && typeof args === 'object' && args.url) {
      url = args.url;
    } else {
      throw new Error('Invalid args format for routed base query');
    }

    // Determine the appropriate base URL
    const baseUrl = getApiBaseUrl(url);
    
    // Create the full URL
    const fullUrl = url.startsWith('http') ? url : `${baseUrl}/${url}`;
    
    // Update args with the full URL
    const updatedArgs = typeof args === 'string' 
      ? fullUrl 
      : { ...args, url: fullUrl };

    // Use the default fetch implementation
    return fetch(updatedArgs, api, extraOptions);
  };
}

// Export configuration constants
export const API_CONFIG = {
  GO_BACKEND_URL,
  NEXTJS_API_URL,
  NEXTJS_ENDPOINTS,
  GO_BACKEND_ENDPOINTS,
} as const;

// Export types
export type { NextJSEndpoint, GoBackendEndpoint };
