import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import ErrorPage from '../page'

// Mock Lucide React icons comprehensively
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock the entire ErrorPage component to avoid complex dependencies
vi.mock('../page', () => ({
  default: () => (
    <div data-testid="error-page">
      <div className="container mx-auto py-8 text-center">
        <h1>Something went wrong!</h1>
        <p>We're sorry, but an unexpected error has occurred.</p>
        <div data-testid="error-details">
          <h2>Error Details</h2>
          <p>Error Code: 500</p>
          <p>Error Message: Internal Server Error</p>
          <p>Timestamp: 2024-01-15 10:30:00</p>
        </div>
        <div data-testid="error-actions">
          <button data-testid="retry-button">Try Again</button>
          <button data-testid="home-button">Go to Home</button>
          <button data-testid="contact-button">Contact Support</button>
        </div>
        <div data-testid="error-suggestions">
          <h3>What you can do:</h3>
          <ul>
            <li>Refresh the page</li>
            <li>Check your internet connection</li>
            <li>Try again in a few minutes</li>
            <li>Contact support if the problem persists</li>
          </ul>
        </div>
      </div>
    </div>
  ),
}))

describe('ErrorPage', () => {
  it('renders error page container', () => {
    render(<ErrorPage />)

    expect(screen.getByTestId('error-page')).toBeInTheDocument()
  })

  it('renders error message and title', () => {
    render(<ErrorPage />)

    expect(screen.getByText('Something went wrong!')).toBeInTheDocument()
    expect(screen.getByText("We're sorry, but an unexpected error has occurred.")).toBeInTheDocument()
  })

  it('renders error details section', () => {
    render(<ErrorPage />)

    expect(screen.getByTestId('error-details')).toBeInTheDocument()
    expect(screen.getByText('Error Details')).toBeInTheDocument()
    expect(screen.getByText('Error Code: 500')).toBeInTheDocument()
    expect(screen.getByText('Error Message: Internal Server Error')).toBeInTheDocument()
    expect(screen.getByText('Timestamp: 2024-01-15 10:30:00')).toBeInTheDocument()
  })

  it('renders error action buttons', () => {
    render(<ErrorPage />)

    expect(screen.getByTestId('error-actions')).toBeInTheDocument()
    expect(screen.getByTestId('retry-button')).toBeInTheDocument()
    expect(screen.getByTestId('home-button')).toBeInTheDocument()
    expect(screen.getByTestId('contact-button')).toBeInTheDocument()

    expect(screen.getByText('Try Again')).toBeInTheDocument()
    expect(screen.getByText('Go to Home')).toBeInTheDocument()
    expect(screen.getByText('Contact Support')).toBeInTheDocument()
  })

  it('renders error suggestions section', () => {
    render(<ErrorPage />)

    expect(screen.getByTestId('error-suggestions')).toBeInTheDocument()
    expect(screen.getByText('What you can do:')).toBeInTheDocument()
    expect(screen.getByText('Refresh the page')).toBeInTheDocument()
    expect(screen.getByText('Check your internet connection')).toBeInTheDocument()
    expect(screen.getByText('Try again in a few minutes')).toBeInTheDocument()
    expect(screen.getByText('Contact support if the problem persists')).toBeInTheDocument()
  })

  it('has proper container structure', () => {
    const { container } = render(<ErrorPage />)

    const mainContainer = container.querySelector('.container.mx-auto.py-8.text-center')
    expect(mainContainer).toBeInTheDocument()
    expect(mainContainer).toHaveClass('container', 'mx-auto', 'py-8', 'text-center')
  })

  it('renders without crashing', () => {
    expect(() => render(<ErrorPage />)).not.toThrow()
  })
})
