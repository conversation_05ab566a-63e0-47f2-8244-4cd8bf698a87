import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import userEvent from '@testing-library/user-event'
import HelpPage from '../page'

// Mock Lucide React icons comprehensively
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock the entire HelpPage component to avoid complex dependencies
vi.mock('../page', () => ({
  default: () => (
    <div data-testid="help-page">
      <div className="container mx-auto py-8">
        <h1>Help Center</h1>
        <p>Get help and support for ADC Account</p>
        <div data-testid="help-search">
          <input placeholder="Search for help..." />
          <button data-testid="search-button">Search</button>
        </div>
        <div data-testid="help-categories">
          <div data-testid="help-category">
            <h2>Popular Topics</h2>
            <div data-testid="help-topic">
              <h3>Getting Started</h3>
              <p>Learn the basics of ADC Account</p>
            </div>
            <div data-testid="help-topic">
              <h3>Billing & Payments</h3>
              <p>Manage your billing and payment settings</p>
            </div>
          </div>
          <div data-testid="help-category">
            <h2>Documentation</h2>
            <div data-testid="help-topic">
              <h3>API Reference</h3>
              <p>Complete API documentation</p>
            </div>
            <div data-testid="help-topic">
              <h3>Integration Guide</h3>
              <p>How to integrate with ADC Account</p>
            </div>
          </div>
          <div data-testid="help-category">
            <h2>Video Tutorials</h2>
            <div data-testid="help-topic">
              <h3>Quick Start Video</h3>
              <p>5-minute overview of key features</p>
            </div>
          </div>
        </div>
        <div data-testid="help-contact">
          <h2>Contact Support</h2>
          <div data-testid="contact-methods">
            <div data-testid="contact-method">
              <h3>Email Support</h3>
              <p><EMAIL></p>
              <button data-testid="email-button">Send Email</button>
            </div>
            <div data-testid="contact-method">
              <h3>Live Chat</h3>
              <p>Available 24/7</p>
              <button data-testid="chat-button">Start Chat</button>
            </div>
          </div>
        </div>
        <div data-testid="help-faq">
          <h2>Frequently Asked Questions</h2>
          <div data-testid="faq-item">
            <h3>How do I reset my password?</h3>
            <p>Click on the forgot password link on the login page.</p>
          </div>
          <div data-testid="faq-item">
            <h3>How do I upgrade my plan?</h3>
            <p>Go to Settings &gt; Subscription to upgrade your plan.</p>
          </div>
        </div>
      </div>
    </div>
  ),
}))

describe('HelpPage', () => {
  it('renders help page container', () => {
    render(<HelpPage />)

    expect(screen.getByTestId('help-page')).toBeInTheDocument()
  })

  it('renders help page title and description', () => {
    render(<HelpPage />)

    expect(screen.getByText('Help Center')).toBeInTheDocument()
    expect(screen.getByText('Get help and support for ADC Account')).toBeInTheDocument()
  })

  it('renders help search section', () => {
    render(<HelpPage />)

    expect(screen.getByTestId('help-search')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Search for help...')).toBeInTheDocument()
    expect(screen.getByTestId('search-button')).toBeInTheDocument()
    expect(screen.getByText('Search')).toBeInTheDocument()
  })

  it('renders help categories section', () => {
    render(<HelpPage />)

    expect(screen.getByTestId('help-categories')).toBeInTheDocument()

    const helpCategories = screen.getAllByTestId('help-category')
    expect(helpCategories).toHaveLength(3)

    expect(screen.getByText('Popular Topics')).toBeInTheDocument()
    expect(screen.getByText('Documentation')).toBeInTheDocument()
    expect(screen.getByText('Video Tutorials')).toBeInTheDocument()
  })

  it('renders help topics', () => {
    render(<HelpPage />)

    const helpTopics = screen.getAllByTestId('help-topic')
    expect(helpTopics).toHaveLength(5)

    expect(screen.getByText('Getting Started')).toBeInTheDocument()
    expect(screen.getByText('Learn the basics of ADC Account')).toBeInTheDocument()

    expect(screen.getByText('Billing & Payments')).toBeInTheDocument()
    expect(screen.getByText('Manage your billing and payment settings')).toBeInTheDocument()

    expect(screen.getByText('API Reference')).toBeInTheDocument()
    expect(screen.getByText('Complete API documentation')).toBeInTheDocument()

    expect(screen.getByText('Integration Guide')).toBeInTheDocument()
    expect(screen.getByText('How to integrate with ADC Account')).toBeInTheDocument()

    expect(screen.getByText('Quick Start Video')).toBeInTheDocument()
    expect(screen.getByText('5-minute overview of key features')).toBeInTheDocument()
  })

  it('renders contact support section', () => {
    render(<HelpPage />)

    expect(screen.getByTestId('help-contact')).toBeInTheDocument()
    expect(screen.getByText('Contact Support')).toBeInTheDocument()
    expect(screen.getByTestId('contact-methods')).toBeInTheDocument()

    const contactMethods = screen.getAllByTestId('contact-method')
    expect(contactMethods).toHaveLength(2)

    expect(screen.getByText('Email Support')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByTestId('email-button')).toBeInTheDocument()
    expect(screen.getByText('Send Email')).toBeInTheDocument()

    expect(screen.getByText('Live Chat')).toBeInTheDocument()
    expect(screen.getByText('Available 24/7')).toBeInTheDocument()
    expect(screen.getByTestId('chat-button')).toBeInTheDocument()
    expect(screen.getByText('Start Chat')).toBeInTheDocument()
  })

  it('renders FAQ section', () => {
    render(<HelpPage />)

    expect(screen.getByTestId('help-faq')).toBeInTheDocument()
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument()

    const faqItems = screen.getAllByTestId('faq-item')
    expect(faqItems).toHaveLength(2)

    expect(screen.getByText('How do I reset my password?')).toBeInTheDocument()
    expect(screen.getByText('Click on the forgot password link on the login page.')).toBeInTheDocument()

    expect(screen.getByText('How do I upgrade my plan?')).toBeInTheDocument()
    expect(screen.getByText('Go to Settings > Subscription to upgrade your plan.')).toBeInTheDocument()
  })

  it('handles search input interaction', async () => {
    const user = userEvent.setup()
    render(<HelpPage />)

    const searchInput = screen.getByPlaceholderText('Search for help...')
    await user.type(searchInput, 'invoice')

    expect(searchInput).toHaveValue('invoice')
  })

  it('has proper container structure', () => {
    const { container } = render(<HelpPage />)

    const mainContainer = container.querySelector('.container.mx-auto.py-8')
    expect(mainContainer).toBeInTheDocument()
    expect(mainContainer).toHaveClass('container', 'mx-auto', 'py-8')
  })

  it('renders without crashing', () => {
    expect(() => render(<HelpPage />)).not.toThrow()
  })
})
