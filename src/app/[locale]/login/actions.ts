'use server'

import { redirect } from 'next/navigation'

// Note: Login is now handled by NextAuth through the credentials provider
// This file is kept for compatibility but the actual login logic is in
// src/app/api/auth/[...nextauth]/options.ts

export async function login(formData: FormData) {
  // This function is deprecated - use NextAuth signIn instead
  console.warn('login() action is deprecated. Use NextAuth signIn instead.')
  return redirect('/login?error=Please use the login form')
}

export async function signup(formData: FormData) {
  // This function is deprecated - use the register page action instead
  console.warn('signup() action is deprecated. Use register page action instead.')
  return redirect('/register')
}
