import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import BankingPage from '../page'

// Mock Redux API hooks
vi.mock('@/redux/services/bankingApi', () => ({
  useGetBankingQuery: vi.fn(),
  useCreateBankingMutation: vi.fn(),
  useUpdateBankingMutation: vi.fn(),
  useDeleteBankingMutation: vi.fn(),
}))

// Mock custom hooks
vi.mock('@/hooks/useI18n', () => ({
  useI18n: () => ({
    t: (key: string) => key,
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatDate: (date: Date) => date.toLocaleDateString(),
  }),
}))

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleApiError: vi.fn(),
  }),
}))

// Mock components
vi.mock('@/components/common/StateManager', () => ({
  default: ({ children, isLoading, error, data }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error occurred</div>
    if (!data) return <div data-testid="no-data">No data</div>
    return <div data-testid="state-manager">{children}</div>
  },
}))

describe('BankingPage', () => {
  const mockUseGetQuery = vi.fn()
  const mockUseCreateMutation = vi.fn()
  const mockUseUpdateMutation = vi.fn()
  const mockUseDeleteMutation = vi.fn()

  const mockData = [
    {
      id: '1',
      name: 'Test Item 1',
      createdAt: new Date(),
    },
    {
      id: '2',
      name: 'Test Item 2',
      createdAt: new Date(),
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()

    mockUseGetQuery.mockReturnValue({
      data: mockData,
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })

    mockUseCreateMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])

    mockUseUpdateMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])

    mockUseDeleteMutation.mockReturnValue([
      vi.fn(),
      { isLoading: false, error: null },
    ])
  })

  it('renders page title', () => {
    render(<BankingPage />)

    const title = screen.queryByRole('heading') || screen.queryByText(/title/i)
    if (title) {
      expect(title).toBeInTheDocument()
    }
  })

  it('shows loading state when data is loading', () => {
    mockUseGetQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      refetch: vi.fn(),
    })

    render(<BankingPage />)
    expect(screen.getByTestId('loading')).toBeInTheDocument()
  })

  it('shows error state when there is an error', () => {
    mockUseGetQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Failed to load data' },
      refetch: vi.fn(),
    })

    render(<BankingPage />)
    expect(screen.getByTestId('error')).toBeInTheDocument()
  })

  it('renders data when available', () => {
    render(<BankingPage />)

    expect(screen.getByTestId('state-manager')).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    render(<BankingPage />)

    // Look for interactive elements
    const buttons = screen.queryAllByRole('button')
    if (buttons.length > 0) {
      await user.click(buttons[0])
    }
  })

  it('has proper accessibility attributes', () => {
    render(<BankingPage />)

    const main = screen.queryByRole('main')
    if (main) {
      expect(main).toBeInTheDocument()
    }
  })
})
