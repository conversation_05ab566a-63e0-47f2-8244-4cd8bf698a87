import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import Home from '../page'

// Mock useRouter
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock next-intl
const mockUseLocale = vi.fn(() => 'en')
vi.mock('next-intl', () => ({
  useLocale: () => mockUseLocale(),
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      'redirecting': 'Redirecting...',
    }
    return translations[key] || key
  },
}))

describe('Home Page', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUseLocale.mockReturnValue('en')
  })

  it('renders loading spinner and redirecting text', () => {
    render(<Home />)

    expect(screen.getByText('Redirecting...')).toBeInTheDocument()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('redirects to login page with correct locale', () => {
    render(<Home />)

    expect(mockPush).toHaveBeenCalledWith('/en/login')
  })

  it('redirects with different locale', () => {
    // Mock useLocale to return 'th' for this test
    mockUseLocale.mockReturnValue('th')

    render(<Home />)

    expect(mockPush).toHaveBeenCalledWith('/th/login')
  })

  it('has correct CSS classes for loading state', () => {
    render(<Home />)

    const container = screen.getByText('Redirecting...').closest('div')
    expect(container).toHaveClass('text-center')

    const spinner = screen.getByTestId('loading-spinner')
    expect(spinner).toHaveClass(
      'animate-spin',
      'rounded-full',
      'h-12',
      'w-12',
      'border-t-2',
      'border-b-2',
      'border-primary',
      'mx-auto',
      'mb-4'
    )
  })
})
