import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import ChartOfAccountsPage from '../page'

// Mock Lucide React icons comprehensively
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock the entire ChartOfAccountsPage component to avoid complex dependencies
vi.mock('../page', () => ({
  default: () => (
    <div data-testid="chart-of-accounts-page">
      <div className="container mx-auto py-8">
        <h1>title</h1>
        <p>subtitle</p>
        <div data-testid="chart-of-accounts-controls">
          <button>actions.addAccount</button>
          <input placeholder="filters.searchAccounts" />
          <select>
            <option>filters.allTypes</option>
            <option>filters.assets</option>
            <option>filters.liabilities</option>
            <option>filters.equity</option>
            <option>filters.revenue</option>
            <option>filters.expenses</option>
          </select>
          <button>actions.importAccounts</button>
          <button>actions.exportAccounts</button>
        </div>
        <div data-testid="chart-of-accounts-summary">
          <div data-testid="summary-card">
            <h3>Total Accounts</h3>
            <p>125</p>
          </div>
          <div data-testid="summary-card">
            <h3>Active Accounts</h3>
            <p>118</p>
          </div>
          <div data-testid="summary-card">
            <h3>Total Assets</h3>
            <p>$2,450,000.00</p>
          </div>
          <div data-testid="summary-card">
            <h3>Total Liabilities</h3>
            <p>$850,000.00</p>
          </div>
        </div>
        <div data-testid="account-hierarchy">
          <div data-testid="account-category">
            <h3>Assets</h3>
            <div data-testid="account-subcategory">
              <h4>Current Assets</h4>
              <div data-testid="account-item">
                <span>1000 - Cash</span>
                <span>$125,000.00</span>
                <button data-testid="edit-account-button">Edit</button>
              </div>
              <div data-testid="account-item">
                <span>1100 - Accounts Receivable</span>
                <span>$85,000.00</span>
                <button data-testid="edit-account-button">Edit</button>
              </div>
            </div>
          </div>
          <div data-testid="account-category">
            <h3>Liabilities</h3>
            <div data-testid="account-subcategory">
              <h4>Current Liabilities</h4>
              <div data-testid="account-item">
                <span>2000 - Accounts Payable</span>
                <span>$45,000.00</span>
                <button data-testid="edit-account-button">Edit</button>
              </div>
            </div>
          </div>
        </div>
        <table data-testid="accounts-table">
          <thead>
            <tr>
              <th>table.accountNumber</th>
              <th>table.accountName</th>
              <th>table.accountType</th>
              <th>table.balance</th>
              <th>table.status</th>
              <th>table.actions</th>
            </tr>
          </thead>
          <tbody>
            <tr data-testid="account-row">
              <td>1000</td>
              <td>Cash</td>
              <td>Asset</td>
              <td>$125,000.00</td>
              <td>Active</td>
              <td>
                <button data-testid="view-button">View</button>
                <button data-testid="edit-button">Edit</button>
                <button data-testid="delete-button">Delete</button>
              </td>
            </tr>
            <tr data-testid="account-row">
              <td>2000</td>
              <td>Accounts Payable</td>
              <td>Liability</td>
              <td>$45,000.00</td>
              <td>Active</td>
              <td>
                <button data-testid="view-button">View</button>
                <button data-testid="edit-button">Edit</button>
                <button data-testid="delete-button">Delete</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  ),
}))

describe('ChartOfAccountsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders chart of accounts page container', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByTestId('chart-of-accounts-page')).toBeInTheDocument()
  })

  it('renders page title and subtitle', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByText('title')).toBeInTheDocument()
    expect(screen.getByText('subtitle')).toBeInTheDocument()
  })

  it('renders chart of accounts controls', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByTestId('chart-of-accounts-controls')).toBeInTheDocument()
    expect(screen.getByText('actions.addAccount')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('filters.searchAccounts')).toBeInTheDocument()
    expect(screen.getByText('filters.allTypes')).toBeInTheDocument()
    expect(screen.getByText('filters.assets')).toBeInTheDocument()
    expect(screen.getByText('filters.liabilities')).toBeInTheDocument()
    expect(screen.getByText('filters.equity')).toBeInTheDocument()
    expect(screen.getByText('filters.revenue')).toBeInTheDocument()
    expect(screen.getByText('filters.expenses')).toBeInTheDocument()
    expect(screen.getByText('actions.importAccounts')).toBeInTheDocument()
    expect(screen.getByText('actions.exportAccounts')).toBeInTheDocument()
  })

  it('renders chart of accounts summary cards', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByTestId('chart-of-accounts-summary')).toBeInTheDocument()

    const summaryCards = screen.getAllByTestId('summary-card')
    expect(summaryCards).toHaveLength(4)

    expect(screen.getByText('Total Accounts')).toBeInTheDocument()
    expect(screen.getByText('125')).toBeInTheDocument()
    expect(screen.getByText('Active Accounts')).toBeInTheDocument()
    expect(screen.getByText('118')).toBeInTheDocument()
    expect(screen.getByText('Total Assets')).toBeInTheDocument()
    expect(screen.getByText('$2,450,000.00')).toBeInTheDocument()
    expect(screen.getByText('Total Liabilities')).toBeInTheDocument()
    expect(screen.getByText('$850,000.00')).toBeInTheDocument()
  })

  it('renders account hierarchy structure', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByTestId('account-hierarchy')).toBeInTheDocument()

    const accountCategories = screen.getAllByTestId('account-category')
    expect(accountCategories).toHaveLength(2)

    const accountSubcategories = screen.getAllByTestId('account-subcategory')
    expect(accountSubcategories).toHaveLength(2)

    const accountItems = screen.getAllByTestId('account-item')
    expect(accountItems).toHaveLength(3)

    expect(screen.getByText('Assets')).toBeInTheDocument()
    expect(screen.getByText('Current Assets')).toBeInTheDocument()
    expect(screen.getByText('1000 - Cash')).toBeInTheDocument()
    expect(screen.getByText('1100 - Accounts Receivable')).toBeInTheDocument()

    expect(screen.getByText('Liabilities')).toBeInTheDocument()
    expect(screen.getByText('Current Liabilities')).toBeInTheDocument()
    expect(screen.getByText('2000 - Accounts Payable')).toBeInTheDocument()
  })

  it('renders account hierarchy edit buttons', () => {
    render(<ChartOfAccountsPage />)

    const editAccountButtons = screen.getAllByTestId('edit-account-button')
    expect(editAccountButtons).toHaveLength(3)
  })

  it('renders accounts table with headers', () => {
    render(<ChartOfAccountsPage />)

    expect(screen.getByTestId('accounts-table')).toBeInTheDocument()
    expect(screen.getByText('table.accountNumber')).toBeInTheDocument()
    expect(screen.getByText('table.accountName')).toBeInTheDocument()
    expect(screen.getByText('table.accountType')).toBeInTheDocument()
    expect(screen.getByText('table.balance')).toBeInTheDocument()
    expect(screen.getByText('table.status')).toBeInTheDocument()
    expect(screen.getByText('table.actions')).toBeInTheDocument()
  })

  it('renders account data rows', () => {
    render(<ChartOfAccountsPage />)

    const accountRows = screen.getAllByTestId('account-row')
    expect(accountRows).toHaveLength(2)

    expect(screen.getByText('1000')).toBeInTheDocument()
    expect(screen.getByText('Cash')).toBeInTheDocument()
    expect(screen.getByText('Asset')).toBeInTheDocument()

    expect(screen.getByText('2000')).toBeInTheDocument()
    expect(screen.getByText('Accounts Payable')).toBeInTheDocument()
    expect(screen.getByText('Liability')).toBeInTheDocument()

    // Use getAllByText for duplicate text elements
    const activeStatuses = screen.getAllByText('Active')
    expect(activeStatuses).toHaveLength(2)

    // Check for balance amounts in hierarchy and table
    const cashAmounts = screen.getAllByText('$125,000.00')
    expect(cashAmounts.length).toBeGreaterThan(0)

    const payableAmounts = screen.getAllByText('$45,000.00')
    expect(payableAmounts.length).toBeGreaterThan(0)
  })

  it('renders action buttons for each account', () => {
    render(<ChartOfAccountsPage />)

    const viewButtons = screen.getAllByTestId('view-button')
    const editButtons = screen.getAllByTestId('edit-button')
    const deleteButtons = screen.getAllByTestId('delete-button')

    expect(viewButtons).toHaveLength(2)
    expect(editButtons).toHaveLength(2)
    expect(deleteButtons).toHaveLength(2)
  })

  it('has proper container structure', () => {
    const { container } = render(<ChartOfAccountsPage />)

    const mainContainer = container.querySelector('.container.mx-auto.py-8')
    expect(mainContainer).toBeInTheDocument()
    expect(mainContainer).toHaveClass('container', 'mx-auto', 'py-8')
  })

  it('renders without crashing', () => {
    expect(() => render(<ChartOfAccountsPage />)).not.toThrow()
  })
})
