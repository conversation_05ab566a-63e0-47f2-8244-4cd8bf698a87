'use server'

import { redirect } from 'next/navigation'
import { createClient } from '@supabase/supabase-js'
import prisma from '@/lib/prisma'
import { ensureUserPersonalMerchant } from '@/lib/utils/auth'

export async function signup(formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string
  const name = formData.get('name') as string

  if (!email || !password) {
    return {
      error: 'Email and password are required',
    }
  }

  try {
    // Create user in Supabase Auth
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    )

    const { data, error } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm for development
      user_metadata: {
        name: name || undefined,
      }
    })

    if (error) {
      console.error('Supabase registration error:', error)
      return {
        error: error.message,
      }
    }

    if (!data.user) {
      return {
        error: 'Failed to create user',
      }
    }

    // Create user in database
    const dbUser = await prisma.user.create({
      data: {
        id: data.user.id,
        email: data.user.email!,
        name: name || null,
        emailVerified: new Date(), // Auto-verify for development
      }
    })

    // Ensure user has a personal merchant
    await ensureUserPersonalMerchant(dbUser.id, dbUser.email, dbUser.name || undefined)

    return redirect('/login?message=Account created successfully. Please sign in.')
  } catch (error: any) {
    console.error('Registration error:', error)
    return {
      error: error.message || 'Registration failed',
    }
  }
}
