'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Link, useRouter } from '@/i18n/navigation';
import { useSearchParams } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { signup } from './actions';
import { useAuth } from '@/hooks/useAuth';

export default function RegisterPage() {
  const t = useTranslations('auth');
  const common = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const message = searchParams.get('message');

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Use NextAuth to check if user is already logged in
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated) {
      console.log('User is already authenticated, redirecting to dashboard');
      toast.success(common('alreadyLoggedIn'));
      router.push('/dashboard');
    }
  }, [isAuthenticated, router, common]);

  // Show message from URL if present
  useEffect(() => {
    if (message) {
      toast.info(message);
    }
  }, [message]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate passwords match
    if (password !== confirmPassword) {
      toast.error(common('passwordMismatch'));
      setIsLoading(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append('email', email);
      formData.append('password', password);
      if (name) formData.append('name', name);

      console.log("Submitting registration data:", { email, name: name || "(not provided)" });

      const result = await signup(formData);

      if (result && 'error' in result) {
        toast.error(common('error'), { description: result.error });
      } else {
        toast.success(common('success'), {
          description: common('accountCreated'),
        });
      }
    } catch (err: any) {
      console.error('Registration request failed:', err);
      const errorMessage = err.message || common('error');
      toast.error(common('error'), { description: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>{common('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">{common('createAccount')}</CardTitle>
          <CardDescription>{common('registerDetails')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">{common('name')} ({common('optional')})</Label>
              <Input
                id="name"
                name="name"
                type="text"
                placeholder="John Doe"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{common('email')}</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{common('password')}</Label>
              <Input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground">{common('passwordRequirements')}</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">{common('confirmPassword')}</Label>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {common('processing')}
                </>
              ) : (
                common('signUp')
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="text-center text-sm">
          {common('alreadyHaveAccount')}{' '}
          <Link href={`/${locale}/login`} className="underline hover:text-primary">
            {common('signIn')}
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
