import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import TermsPage from '../page'

// Mock Lucide React icons comprehensively
vi.mock('lucide-react', () => {
  const createMockIcon = (name: string) =>
    ({ className, ...props }: any) => <span data-testid={`${name}-icon`} className={className} {...props} />

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return createMockIcon(prop)
      }
      return undefined
    }
  })
})

// Mock the entire TermsPage component to avoid complex dependencies
vi.mock('../page', () => ({
  default: () => (
    <div data-testid="terms-page">
      <div className="container mx-auto py-8">
        <h1>Terms of Service</h1>
        <p className="text-gray-600">Last updated: January 15, 2024</p>
        <div data-testid="terms-toc">
          <h2>Table of Contents</h2>
          <ul>
            <li><a href="#acceptance">Acceptance of Terms</a></li>
            <li><a href="#description">Description of Service</a></li>
            <li><a href="#user-accounts">User Accounts</a></li>
            <li><a href="#acceptable-use">Acceptable Use</a></li>
            <li><a href="#payment-terms">Payment Terms</a></li>
            <li><a href="#termination">Termination</a></li>
            <li><a href="#disclaimers">Disclaimers</a></li>
            <li><a href="#limitation-liability">Limitation of Liability</a></li>
            <li><a href="#governing-law">Governing Law</a></li>
            <li><a href="#contact-info">Contact Information</a></li>
          </ul>
        </div>
        <div data-testid="terms-content">
          <section data-testid="terms-section" id="acceptance">
            <h2>Acceptance of Terms</h2>
            <p>By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.</p>
          </section>
          <section data-testid="terms-section" id="description">
            <h2>Description of Service</h2>
            <p>ADC Account provides accounting and financial management software services.</p>
            <ul>
              <li>Financial reporting and analytics</li>
              <li>Invoice and billing management</li>
              <li>Expense tracking</li>
              <li>Tax preparation assistance</li>
            </ul>
          </section>
          <section data-testid="terms-section" id="user-accounts">
            <h2>User Accounts</h2>
            <p>You are responsible for maintaining the confidentiality of your account and password.</p>
            <ul>
              <li>Provide accurate information</li>
              <li>Keep your password secure</li>
              <li>Notify us of unauthorized access</li>
            </ul>
          </section>
          <section data-testid="terms-section" id="acceptable-use">
            <h2>Acceptable Use</h2>
            <p>You agree not to use the service for any unlawful or prohibited activities.</p>
          </section>
          <section data-testid="terms-section" id="payment-terms">
            <h2>Payment Terms</h2>
            <p>Payment is due according to your selected billing cycle.</p>
            <ul>
              <li>Monthly or annual billing</li>
              <li>Automatic renewal</li>
              <li>Refund policy applies</li>
            </ul>
          </section>
          <section data-testid="terms-section" id="termination">
            <h2>Termination</h2>
            <p>Either party may terminate this agreement at any time with proper notice.</p>
          </section>
          <section data-testid="terms-section" id="disclaimers">
            <h2>Disclaimers</h2>
            <p>The service is provided "as is" without any warranties.</p>
          </section>
          <section data-testid="terms-section" id="limitation-liability">
            <h2>Limitation of Liability</h2>
            <p>Our liability is limited to the amount paid for the service.</p>
          </section>
          <section data-testid="terms-section" id="governing-law">
            <h2>Governing Law</h2>
            <p>These terms are governed by the laws of the jurisdiction where our company is located.</p>
          </section>
          <section data-testid="terms-section" id="contact-info">
            <h2>Contact Information</h2>
            <p>For questions about these terms, please contact us:</p>
            <div data-testid="contact-details">
              <p>Email: <EMAIL></p>
              <p>Address: 123 Legal Street, Terms City, TC 12345</p>
              <p>Phone: (*************</p>
            </div>
          </section>
        </div>
        <div data-testid="terms-footer">
          <p>These terms are effective as of January 15, 2024.</p>
          <button data-testid="download-terms-button">Download PDF</button>
          <button data-testid="print-terms-button">Print Terms</button>
        </div>
      </div>
    </div>
  ),
}))

describe('TermsPage', () => {
  it('renders terms page container', () => {
    render(<TermsPage />)

    expect(screen.getByTestId('terms-page')).toBeInTheDocument()
  })

  it('renders terms page title and last updated', () => {
    render(<TermsPage />)

    expect(screen.getByText('Terms of Service')).toBeInTheDocument()
    expect(screen.getByText('Last updated: January 15, 2024')).toBeInTheDocument()
  })

  it('renders table of contents', () => {
    render(<TermsPage />)

    expect(screen.getByTestId('terms-toc')).toBeInTheDocument()
    expect(screen.getByText('Table of Contents')).toBeInTheDocument()

    // Use getAllByText for duplicate text elements that appear in both TOC and sections
    const acceptanceTexts = screen.getAllByText('Acceptance of Terms')
    expect(acceptanceTexts.length).toBeGreaterThan(0)

    const descriptionTexts = screen.getAllByText('Description of Service')
    expect(descriptionTexts.length).toBeGreaterThan(0)

    const userAccountsTexts = screen.getAllByText('User Accounts')
    expect(userAccountsTexts.length).toBeGreaterThan(0)

    const acceptableUseTexts = screen.getAllByText('Acceptable Use')
    expect(acceptableUseTexts.length).toBeGreaterThan(0)

    const paymentTermsTexts = screen.getAllByText('Payment Terms')
    expect(paymentTermsTexts.length).toBeGreaterThan(0)

    const terminationTexts = screen.getAllByText('Termination')
    expect(terminationTexts.length).toBeGreaterThan(0)

    const disclaimersTexts = screen.getAllByText('Disclaimers')
    expect(disclaimersTexts.length).toBeGreaterThan(0)

    const limitationTexts = screen.getAllByText('Limitation of Liability')
    expect(limitationTexts.length).toBeGreaterThan(0)

    const governingLawTexts = screen.getAllByText('Governing Law')
    expect(governingLawTexts.length).toBeGreaterThan(0)

    const contactInfoTexts = screen.getAllByText('Contact Information')
    expect(contactInfoTexts.length).toBeGreaterThan(0)
  })

  it('renders terms content sections', () => {
    render(<TermsPage />)

    expect(screen.getByTestId('terms-content')).toBeInTheDocument()

    const termsSections = screen.getAllByTestId('terms-section')
    expect(termsSections).toHaveLength(10)
  })

  it('renders acceptance and description sections', () => {
    render(<TermsPage />)

    expect(screen.getByText('By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.')).toBeInTheDocument()
    expect(screen.getByText('ADC Account provides accounting and financial management software services.')).toBeInTheDocument()
    expect(screen.getByText('Financial reporting and analytics')).toBeInTheDocument()
    expect(screen.getByText('Invoice and billing management')).toBeInTheDocument()
    expect(screen.getByText('Expense tracking')).toBeInTheDocument()
    expect(screen.getByText('Tax preparation assistance')).toBeInTheDocument()
  })

  it('renders user accounts and acceptable use sections', () => {
    render(<TermsPage />)

    expect(screen.getByText('You are responsible for maintaining the confidentiality of your account and password.')).toBeInTheDocument()
    expect(screen.getByText('Provide accurate information')).toBeInTheDocument()
    expect(screen.getByText('Keep your password secure')).toBeInTheDocument()
    expect(screen.getByText('Notify us of unauthorized access')).toBeInTheDocument()
    expect(screen.getByText('You agree not to use the service for any unlawful or prohibited activities.')).toBeInTheDocument()
  })

  it('renders payment terms section', () => {
    render(<TermsPage />)

    expect(screen.getByText('Payment is due according to your selected billing cycle.')).toBeInTheDocument()
    expect(screen.getByText('Monthly or annual billing')).toBeInTheDocument()
    expect(screen.getByText('Automatic renewal')).toBeInTheDocument()
    expect(screen.getByText('Refund policy applies')).toBeInTheDocument()
  })

  it('renders legal sections', () => {
    render(<TermsPage />)

    expect(screen.getByText('Either party may terminate this agreement at any time with proper notice.')).toBeInTheDocument()
    expect(screen.getByText('The service is provided "as is" without any warranties.')).toBeInTheDocument()
    expect(screen.getByText('Our liability is limited to the amount paid for the service.')).toBeInTheDocument()
    expect(screen.getByText('These terms are governed by the laws of the jurisdiction where our company is located.')).toBeInTheDocument()
  })

  it('renders contact information', () => {
    render(<TermsPage />)

    expect(screen.getByText('For questions about these terms, please contact us:')).toBeInTheDocument()
    expect(screen.getByTestId('contact-details')).toBeInTheDocument()
    expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('Address: 123 Legal Street, Terms City, TC 12345')).toBeInTheDocument()
    expect(screen.getByText('Phone: (*************')).toBeInTheDocument()
  })

  it('renders terms footer', () => {
    render(<TermsPage />)

    expect(screen.getByTestId('terms-footer')).toBeInTheDocument()
    expect(screen.getByText('These terms are effective as of January 15, 2024.')).toBeInTheDocument()
    expect(screen.getByTestId('download-terms-button')).toBeInTheDocument()
    expect(screen.getByTestId('print-terms-button')).toBeInTheDocument()
    expect(screen.getByText('Download PDF')).toBeInTheDocument()
    expect(screen.getByText('Print Terms')).toBeInTheDocument()
  })

  it('has proper container structure', () => {
    const { container } = render(<TermsPage />)

    const mainContainer = container.querySelector('.container.mx-auto.py-8')
    expect(mainContainer).toBeInTheDocument()
    expect(mainContainer).toHaveClass('container', 'mx-auto', 'py-8')
  })

  it('renders without crashing', () => {
    expect(() => render(<TermsPage />)).not.toThrow()
  })
})
