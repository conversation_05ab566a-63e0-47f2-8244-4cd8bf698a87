import { NextResponse } from 'next/server';

/**
 * Health check endpoint for Cloud Run
 * This helps verify that the application is running correctly
 */
export async function GET() {
  try {
    // Check if we can access environment variables
    const envCheck = {
      nodeEnv: process.env.NODE_ENV || 'not set',
      databaseUrl: process.env.DATABASE_URL ? 'set' : 'not set',
      directUrl: process.env.DIRECT_URL ? 'set' : 'not set',
      nextAuthSecret: process.env.NEXTAUTH_SECRET ? 'set' : 'not set',
      nextAuthUrl: process.env.NEXTAUTH_URL || 'not set',
    };

    return NextResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      environment: envCheck,
    });
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
