import { NextRequest, NextResponse } from 'next/server';
import { proxyGet } from '@/lib/utils/proxy';

/**
 * Health check endpoint for Cloud Run
 * This helps verify that the application is running correctly
 * Proxies to the Rust backend's health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // First check if we can access environment variables
    const envCheck = {
      nodeEnv: process.env.NODE_ENV || 'not set',
      databaseUrl: process.env.DATABASE_URL ? 'set' : 'not set',
      directUrl: process.env.DIRECT_URL ? 'set' : 'not set',
      nextAuthSecret: process.env.NEXTAUTH_SECRET ? 'set' : 'not set',
      nextAuthUrl: process.env.NEXTAUTH_URL || 'not set',
      rustBackendUrl: process.env.RUST_BACKEND_URL || 'not set',
    };

    // Try to proxy to the Rust backend's health check endpoint
    try {
      const backendResponse = await proxyGet('/health', request);
      
      // If the backend is healthy, return a combined health check
      if (backendResponse.status === 200) {
        const backendData = await backendResponse.json();
        
        return NextResponse.json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          frontend: {
            environment: envCheck,
          },
          backend: backendData,
        });
      } else {
        // If the backend is not healthy, return an error
        return NextResponse.json({
          status: 'error',
          message: 'Backend health check failed',
          timestamp: new Date().toISOString(),
          frontend: {
            status: 'ok',
            environment: envCheck,
          },
          backend: {
            status: 'error',
          },
        }, { status: 503 });
      }
    } catch (backendError) {
      // If we can't connect to the backend, return a partial health check
      console.error('Backend health check failed:', backendError);
      
      return NextResponse.json({
        status: 'partial',
        message: 'Frontend is healthy but backend is unreachable',
        timestamp: new Date().toISOString(),
        frontend: {
          status: 'ok',
          environment: envCheck,
        },
        backend: {
          status: 'error',
          message: backendError instanceof Error ? backendError.message : 'Unknown error',
        },
      }, { status: 207 }); // 207 Multi-Status
    }
  } catch (error) {
    console.error('Health check failed:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
