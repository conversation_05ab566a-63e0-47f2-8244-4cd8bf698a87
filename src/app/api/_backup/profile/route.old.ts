import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { ProfileSchema } from '@/lib/validators/profile';
import { z } from 'zod';
import bcrypt from 'bcrypt';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// Removed mock profile data
// const mockProfile = {
//   id: 'user-123',
//   name: 'Test User',
//   email: '<EMAIL>',
//   bio: 'Finance professional with 10 years of experience',
//   phone: '************',
//   jobTitle: 'Financial Controller',
//   department: 'Finance',
//   merchants: [],
//   defaultMerchantId: null,
//   createdAt: '2023-01-01T00:00:00.000Z',
//   updatedAt: '2023-01-01T00:00:00.000Z',
// };

export async function GET(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('GET /api/profile - Authentication failed');
      return errorResponse;
    }

    console.log(`GET /api/profile - Authenticated user: ${userId}`);

    // Get user profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        image: true,
        createdAt: true,
        updatedAt: true,
        merchantPermissions: {
          select: {
            merchantId: true,
            permissionLevel: true,
            merchant: {
              select: {
                id: true,
                name: true,
                logoUrl: true,
              },
            },
          },
        },
        preferences: {
          select: {
            defaultMerchantId: true,
            bio: true,
            phone: true,
            jobTitle: true,
            department: true,
          },
        },
      },
    });

    if (!user) {
      // User not found in database, which shouldn't happen if session is valid
      console.error(`User with ID ${userId} not found in database.`);
      return NextResponse.json({ message: 'User profile not found' }, { status: 404 });
     }

     // Transform the data to include merchant information in a more usable format
    const merchants = user.merchantPermissions.map(permission => ({
      id: permission.merchantId,
      name: permission.merchant.name,
      logoUrl: permission.merchant.logoUrl,
      permissionLevel: permission.permissionLevel,
    }));

    // Combine user data with preferences
    const profile = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      image: user.image,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      merchants,
      defaultMerchantId: user.preferences?.defaultMerchantId || null,
      bio: user.preferences?.bio || '',
      phone: user.preferences?.phone || '',
      jobTitle: user.preferences?.jobTitle || '',
      department: user.preferences?.department || '',
    };

    return NextResponse.json(profile);
  } catch (error) {
    console.error('Error fetching profile:', error);
    // Propagate the error without falling back to mock data
    if (error instanceof Error && error.message.includes('Unauthorized')) {
        return NextResponse.json({ message: error.message }, { status: 401 });
    }
    return NextResponse.json({ message: 'Failed to fetch profile due to server error' }, { status: 500 });
   }
 }

export async function PUT(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('PUT /api/profile - Authentication failed');
      return errorResponse;
    }

    console.log(`PUT /api/profile - Authenticated user: ${userId}`);

    const body = await request.json();
    const validation = ProfileSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ message: 'Invalid input', errors: validation.error.format() }, { status: 400 });
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      // User not found in database, which shouldn't happen if session is valid
      console.error(`User with ID ${userId} not found for update.`);
      return NextResponse.json({ message: 'User profile not found' }, { status: 404 });
     }

     // Prepare update data for user
    const userUpdateData: any = {};
    if (body.name) userUpdateData.name = body.name;
    if (body.email) userUpdateData.email = body.email;
    if (body.image) userUpdateData.image = body.image;

    // Hash password if provided
    if (body.password) {
      const saltRounds = 10;
      userUpdateData.passwordHash = await bcrypt.hash(body.password, saltRounds);
    }

    // Prepare update data for preferences
    const preferencesUpdateData: any = {};
    if (body.bio !== undefined) preferencesUpdateData.bio = body.bio;
    if (body.phone !== undefined) preferencesUpdateData.phone = body.phone;
    if (body.jobTitle !== undefined) preferencesUpdateData.jobTitle = body.jobTitle;
    if (body.department !== undefined) preferencesUpdateData.department = body.department;
    if (body.defaultMerchantId !== undefined) preferencesUpdateData.defaultMerchantId = body.defaultMerchantId;

    // Update user and preferences in a transaction
    await prisma.$transaction(async (tx) => {
      // Update user
      await tx.user.update({
        where: { id: userId },
        data: userUpdateData,
      });

      // Update or create preferences
      await tx.userPreferences.upsert({
        where: { userId: userId },
        update: preferencesUpdateData,
        create: {
          userId: userId,
          ...preferencesUpdateData,
        },
      });
    });

    // Get updated profile with all data
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        image: true,
        createdAt: true,
        updatedAt: true,
        merchantPermissions: {
          select: {
            merchantId: true,
            permissionLevel: true,
            merchant: {
              select: {
                id: true,
                name: true,
                logoUrl: true,
              },
            },
          },
        },
        preferences: {
          select: {
            defaultMerchantId: true,
            bio: true,
            phone: true,
            jobTitle: true,
            department: true,
          },
        },
      },
    });

    if (!user) {
      // User not found in database, which shouldn't happen if session is valid
      console.error(`User with ID ${userId} not found after update.`);
      return NextResponse.json({ message: 'User profile not found after update' }, { status: 500 });
    }

    // Transform the data to include merchant information in a more usable format
    const merchants = user.merchantPermissions.map(permission => ({
      id: permission.merchantId,
      name: permission.merchant.name,
      logoUrl: permission.merchant.logoUrl,
      permissionLevel: permission.permissionLevel,
    }));

    // Combine user data with preferences
    const profile = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      image: user.image,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      merchants,
      defaultMerchantId: user.preferences?.defaultMerchantId || null,
      bio: user.preferences?.bio || '',
      phone: user.preferences?.phone || '',
      jobTitle: user.preferences?.jobTitle || '',
      department: user.preferences?.department || '',
    };

    return NextResponse.json(profile);
  } catch (error) {
    console.error('Error updating profile:', error);
    if (error instanceof z.ZodError) {
        return NextResponse.json({ message: 'Invalid input', errors: error.format() }, { status: 400 });
    }
    const dbError = error as any;
    if (dbError.code === 'P2002' && dbError.meta?.target?.includes('email')) {
        return NextResponse.json({ message: 'Email address is already in use' }, { status: 409 }); // Conflict
    }
    if (error instanceof Error && error.message.includes('Unauthorized')) {
        return NextResponse.json({ message: error.message }, { status: 401 });
    }
     return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
   }
 }
