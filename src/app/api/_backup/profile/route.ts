import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPut } from '@/lib/utils/proxy';
import { z } from 'zod';

// Zod schema for updating a user profile
const profileSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters long').optional(),
  bio: z.string().optional(),
  phone: z.string().optional(),
  jobTitle: z.string().optional(),
  department: z.string().optional(),
  defaultMerchantId: z.string().uuid('Invalid merchant ID format').optional().nullable(),
  image: z.string().url('Invalid image URL').optional().nullable(),
});

// GET /api/profile - Get the current user's profile
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/profile', request);
  } catch (error) {
    console.error('GET /api/profile error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/profile - Update the current user's profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = profileSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      email: validatedData.email,
      password: validatedData.password,
      bio: validatedData.bio,
      phone: validatedData.phone,
      job_title: validatedData.jobTitle,
      department: validatedData.department,
      default_merchant_id: validatedData.defaultMerchantId,
      image: validatedData.image,
    };

    // Proxy to Rust backend
    return await proxyPut('/profile', request, rustRequestBody);
  } catch (error) {
    console.error('PUT /api/profile error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
