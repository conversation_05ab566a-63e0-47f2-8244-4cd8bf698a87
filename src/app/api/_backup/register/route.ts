import { NextRequest, NextResponse } from 'next/server';
import { proxyPost } from '@/lib/utils/proxy';
import { z } from 'zod';

// Zod schema for user registration
const registerSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters long"),
  businessName: z.string().min(1, "Business name is required").optional(),
});

// POST /api/register - Register a new user and merchant
export async function POST(request: NextRequest) {
  try {
    // Parse and validate the request body
    const body = await request.json();
    const validationResult = registerSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Generate a business name if not provided
    const businessName = validatedData.businessName || 
                         (validatedData.name ? `${validatedData.name}'s Business` : "My Business");
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      email: validatedData.email,
      password: validatedData.password,
      business_name: businessName,
    };

    // Proxy to Rust backend
    // This endpoint doesn't require authentication
    return await proxyPost('/register', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/register error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON body' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
