// src/app/api/register/route.ts
import { NextResponse } from 'next/server';
import prisma from '../../../lib/prisma';
import bcrypt from 'bcrypt';
import { z } from 'zod';
import { SubscriptionPlan, SubscriptionStatus } from '@prisma/client';
import { subscriptionService } from '@/lib/services/subscriptionService';

// Basic validation schema
const registerSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters long"),
});

export async function POST(request: Request) {
  try {
    // Log that we're starting the registration process
    console.log("Starting registration process");

    // Parse request body
    let body;
    try {
      body = await request.json();
      console.log("Request body parsed successfully");
    } catch (e) {
      console.error("Error parsing request body:", e);
      return NextResponse.json({ message: 'Invalid request body' }, { status: 400 });
    }

    // Validate request data
    const validation = registerSchema.safeParse(body);
    if (!validation.success) {
      console.error("Validation error:", validation.error.flatten().fieldErrors);
      return NextResponse.json({
        message: 'Validation failed',
        errors: validation.error.flatten().fieldErrors
      }, { status: 400 });
    }

    const { email, password, name } = validation.data;
    console.log(`Registering user with email: ${email}`);

    // Check if user already exists
    try {
      const existingUser = await prisma.user.findUnique({
        where: { email: email },
      });

      if (existingUser) {
        console.log(`User with email ${email} already exists`);
        return NextResponse.json({ message: 'User already exists' }, { status: 409 });
      }
    } catch (e) {
      console.error("Error checking for existing user:", e);
      return NextResponse.json({
        message: 'Error checking for existing user',
        error: e instanceof Error ? e.message : 'Unknown error'
      }, { status: 500 });
    }

    // Hash password
    let hashedPassword;
    try {
      const saltRounds = 10;
      hashedPassword = await bcrypt.hash(password, saltRounds);
      console.log("Password hashed successfully");
    } catch (e) {
      console.error("Error hashing password:", e);
      return NextResponse.json({
        message: 'Error hashing password',
        error: e instanceof Error ? e.message : 'Unknown error'
      }, { status: 500 });
    }

    // Create user in database
    try {
      console.log("Creating new user in database");

      // Create a new merchant for this user
      console.log("Creating new merchant for user");
      const businessName = name ? `${name}'s Business` : "My Business";
      const merchant = await prisma.merchant.create({
        data: {
          name: businessName,
          primaryContactEmail: email,
        }
      });
      console.log("Created new merchant:", merchant.id);

      // Create the user with transaction to ensure all operations succeed or fail together
      const result = await prisma.$transaction(async (tx) => {
        // Create the user
        const newUser = await tx.user.create({
          data: {
            email: email,
            passwordHash: hashedPassword,
            name: name,
            role: "staff", // Add the role field
            isActive: true, // Add the isActive field
            // Connect the user to the merchant
            merchants: {
              connect: {
                id: merchant.id
              }
            }
          },
          include: {
            merchants: true // Include the connected merchant in the response
          }
        });

        // Create user-merchant permission with Owner level
        await tx.userMerchantPermission.create({
          data: {
            userId: newUser.id,
            merchantId: merchant.id,
            permissionLevel: 'Owner', // Set as Owner for their own merchant
          }
        });

        // Create a free subscription for the merchant
        const now = new Date();
        const oneYearFromNow = new Date(now);
        oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

        const subscription = await tx.subscription.create({
          data: {
            merchantId: merchant.id,
            plan: SubscriptionPlan.Free,
            status: SubscriptionStatus.Active,
            currentPeriodStart: now,
            currentPeriodEnd: oneYearFromNow,
            cancelAtPeriodEnd: false,
          }
        });

        console.log("Created free subscription for merchant:", subscription.id);

        return { newUser, subscription };
      });

      // Don't return password hash
      const { passwordHash, ...userWithoutPassword } = result.newUser;
      console.log("User created successfully:", userWithoutPassword.id);
      console.log("Free subscription created successfully:", result.subscription.id);

      return NextResponse.json({
        ...userWithoutPassword,
        subscription: {
          plan: result.subscription.plan,
          status: result.subscription.status,
          currentPeriodEnd: result.subscription.currentPeriodEnd
        }
      }, { status: 201 });
    } catch (e) {
      console.error("Error creating user:", e);

      // Provide more detailed error information
      let errorMessage = 'Error creating user';
      let errorDetails = e instanceof Error ? e.message : 'Unknown error';

      // Check for Prisma-specific errors
      if (e instanceof Error && 'code' in e) {
        const prismaError = e as any;
        if (prismaError.code === 'P2002') {
          errorMessage = 'User with this email already exists';
        } else if (prismaError.code === 'P2025') {
          errorMessage = 'Record not found';
        }

        console.error("Prisma error code:", prismaError.code);
        console.error("Prisma error meta:", prismaError.meta);
      }

      return NextResponse.json({
        message: errorMessage,
        error: errorDetails
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Unhandled registration error:", error);

    // Type guard for error object
    let errorMessage = 'Internal Server Error';
    let errorDetails = 'Unknown error occurred';

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || 'No stack trace available';
    }

    return NextResponse.json({
      message: 'An error occurred during registration.',
      error: errorMessage,
      details: errorDetails
    }, { status: 500 });
  }
}