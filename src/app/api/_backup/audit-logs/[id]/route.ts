// src/app/api/audit-logs/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import auditLogService from '@/lib/services/auditLogService';

// GET /api/audit-logs/:id - Get a specific audit log
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    // Check if the user has permission to view audit logs (Admin level or higher)
    const permissionCheck = await requirePermission(request, merchantId, PermissionLevel.Admin);
    if (permissionCheck) {
      return permissionCheck;
    }

    // Get the audit log
    const log = await auditLogService.findById(id);

    // Check if the log exists and belongs to the merchant
    if (!log || (log.merchantId && log.merchantId !== merchantId)) {
      return NextResponse.json({ message: 'Audit log not found' }, { status: 404 });
    }

    // Format the response
    const formattedLog = {
      id: log.id,
      user_id: log.userId,
      user_name: log.User?.name,
      user_email: log.User?.email,
      merchant_id: log.merchantId,
      action: log.action,
      resource: log.resource,
      resource_id: log.resourceId,
      details: log.details,
      ip_address: log.ipAddress,
      user_agent: log.userAgent,
      path: log.path,
      method: log.method,
      status_code: log.statusCode,
      timestamp: log.timestamp.toISOString(),
    };

    return NextResponse.json(formattedLog);
  } catch (error) {
    console.error('Error fetching audit log:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
