import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPost } from '@/lib/utils/proxy';
import { z } from 'zod';

// GET /api/audit-logs - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Check if the user has admin role
    if (session.user.role !== 'Admin') {
      return NextResponse.json({ message: 'Forbidden: Admin access required' }, { status: 403 });
    }

    return await proxyGet('/audit-logs', request);
  } catch (error) {
    console.error('GET /api/audit-logs error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// POST /api/audit-logs - Create a new audit log
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    
    // Create a schema for audit log creation
    const createAuditLogSchema = z.object({
      action: z.string().min(1, 'Action is required'),
      resource: z.string().min(1, 'Resource is required'),
      resourceId: z.string().optional(),
      details: z.record(z.any()).optional(),
      ipAddress: z.string().optional(),
      userAgent: z.string().optional(),
      path: z.string().optional(),
      method: z.string().optional(),
      statusCode: z.number().optional(),
    });
    
    const validationResult = createAuditLogSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      user_id: session.user.id,
      action: validatedData.action,
      resource: validatedData.resource,
      resource_id: validatedData.resourceId,
      details: validatedData.details,
      ip_address: validatedData.ipAddress || request.headers.get('x-forwarded-for') || request.ip,
      user_agent: validatedData.userAgent || request.headers.get('user-agent'),
      path: validatedData.path || request.nextUrl.pathname,
      method: validatedData.method || request.method,
      status_code: validatedData.statusCode,
    };

    // Proxy to Rust backend
    return await proxyPost('/audit-logs', request, rustRequestBody);
  } catch (error) {
    console.error('POST /api/audit-logs error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
