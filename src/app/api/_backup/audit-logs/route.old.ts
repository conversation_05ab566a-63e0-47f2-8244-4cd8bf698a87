// src/app/api/audit-logs/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMerchantId } from '@/lib/utils/auth';
import { requirePermission } from '@/lib/utils/permissions';
import { PermissionLevel } from '@prisma/client';
import auditLogService from '@/lib/services/auditLogService';
import { createCustomClient } from '@/utils/supabase/customClient';
import { checkAuth } from '@/utils/api-auth';

// GET /api/audit-logs - Get all audit logs for a merchant
export async function GET(request: NextRequest) {
  try {
    // Check authentication using our helper
    const { isAuthenticated, userId: authenticatedUserId, errorResponse } = await checkAuth(request);

    if (!isAuthenticated) {
      console.error('GET /api/audit-logs - Authentication failed');
      return errorResponse;
    }

    console.log(`GET /api/audit-logs - Authenticated user: ${authenticatedUserId}`);

    // Get the merchant ID from the session
    const merchantId = await getMerchantId();
    if (!merchantId) {
      return NextResponse.json({ message: 'Unauthorized: Merchant ID not found' }, { status: 401 });
    }

    console.log(`GET /api/audit-logs - Using merchant ID: ${merchantId}`);

    // Create a new request with the user ID in the headers
    const requestWithUserId = new NextRequest(request.url, {
      headers: new Headers({
        'x-user-id': authenticatedUserId,
        ...Object.fromEntries(request.headers.entries())
      })
    });

    // Check if the user has permission to view audit logs (Admin level or higher)
    const permissionCheck = await requirePermission(requestWithUserId, merchantId, PermissionLevel.Admin);
    if (permissionCheck) {
      return permissionCheck;
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const action = searchParams.get('action');
    const resource = searchParams.get('resource');
    const resourceId = searchParams.get('resourceId');
    const path = searchParams.get('path');
    const method = searchParams.get('method');
    const statusCode = searchParams.get('statusCode') ? parseInt(searchParams.get('statusCode')!) : undefined;
    const startDateStr = searchParams.get('startDate');
    const endDateStr = searchParams.get('endDate');
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50;
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0;

    // Parse dates if provided
    const startDate = startDateStr ? new Date(startDateStr) : undefined;
    const endDate = endDateStr ? new Date(endDateStr) : undefined;

    // Get audit logs
    const result = await auditLogService.findAllByMerchant(merchantId, {
      userId: userId || undefined,
      action: action || undefined,
      resource: resource || undefined,
      resourceId: resourceId || undefined,
      path: path || undefined,
      method: method || undefined,
      statusCode,
      startDate,
      endDate,
      limit,
      offset,
    });

    // Format the response
    const formattedLogs = result.logs.map(log => ({
      id: log.id,
      user_id: log.userId,
      user_name: log.User?.name,
      user_email: log.User?.email,
      merchant_id: log.merchantId,
      action: log.action,
      resource: log.resource,
      resource_id: log.resourceId,
      details: log.details,
      ip_address: log.ipAddress,
      user_agent: log.userAgent,
      path: log.path,
      method: log.method,
      status_code: log.statusCode,
      timestamp: log.timestamp.toISOString(),
    }));

    return NextResponse.json({
      logs: formattedLogs,
      pagination: {
        total: result.total,
        limit: result.limit,
        offset: result.offset,
      },
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}


