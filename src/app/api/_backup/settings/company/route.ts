import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth/session';
import { proxyGet, proxyPut } from '@/lib/utils/proxy';
import { z } from 'zod';
import { MerchantSettingsSchema } from '@/lib/validators/settings';

// GET /api/settings/company - Proxy to Rust backend
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    return await proxyGet('/settings/company', request);
  } catch (error) {
    console.error('GET /api/settings/company error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}

// PUT /api/settings/company - Validate and proxy to Rust backend
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = MerchantSettingsSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { errors: validationResult.error.errors },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;
    
    // Transform the validated data to match the Rust backend's expected format
    const rustRequestBody = {
      name: validatedData.name,
      address: validatedData.address,
      phone: validatedData.phone,
      primary_contact_email: validatedData.primaryContactEmail,
      website: validatedData.website,
      tax_id: validatedData.taxId,
      fiscal_year_start: validatedData.fiscalYearStart,
      currency: validatedData.currency,
      logo_url: validatedData.logoUrl,
      legal_name: validatedData.legalName,
    };

    // Proxy to Rust backend
    return await proxyPut('/settings/company', request, rustRequestBody);
  } catch (error) {
    console.error('PUT /api/settings/company error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
