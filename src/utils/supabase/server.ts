import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'

/**
 * Creates a Supabase client for server-side usage with proper cookie handling
 * This function ensures all cookie operations are properly awaited
 */
export async function createClient() {
  // For API routes, we'll use a direct client without cookies
  // This avoids the Next.js cookies() synchronous usage issue
  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

/**
 * Creates a Supabase client for server-side usage with cookie handling
 * This is used for pages that need cookie-based auth
 * WARNING: This should NOT be used in API routes due to Next.js cookies() sync issues
 */
export async function createClientWithCookies() {
  try {
    // Get the cookies from the request
    const cookieStore = cookies()

    // Create and return the Supabase client
    return createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            try {
              const cookie = cookieStore.get(name)
              return cookie?.value || ''
            } catch (error) {
              console.error(`Error getting cookie ${name}:`, error)
              return ''
            }
          },
          set(name: string, value: string, options: any) {
            try {
              cookieStore.set({ name, value, ...options })
            } catch (error) {
              console.error(`Error setting cookie ${name}:`, error)
            }
          },
          remove(name: string, options: any) {
            try {
              cookieStore.set({ name, value: '', ...options })
            } catch (error) {
              console.error(`Error removing cookie ${name}:`, error)
            }
          },
        },
      }
    )
  } catch (error) {
    console.error('Error creating client with cookies:', error)

    // Fall back to a direct client without cookies
    return createSupabaseClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    )
  }
}
