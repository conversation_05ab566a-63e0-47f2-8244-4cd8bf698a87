/**
 * End-to-End Restaurant Management Tests
 * Tests for Linear Cards: ADC-48, ADC-49, ADC-81, ADC-82, ADC-83
 * 
 * This file tests the complete restaurant management functionality including:
 * - Restaurant creation and registration
 * - Restaurant details management
 * - Restaurant list/dashboard overview
 * - Restaurant settings and configuration
 * - Staff and table management
 */

const { test, expect } = require('@playwright/test');

test.describe('Restaurant Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/en/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard**');
  });

  test('should navigate to restaurants page', async ({ page }) => {
    // Look for restaurants link in navigation
    const restaurantLink = page.locator('a:has-text("Restaurants"), a:has-text("Restaurant"), button:has-text("Restaurants")');
    
    if (await restaurantLink.first().isVisible()) {
      await restaurantLink.first().click();
      await page.waitForURL('**/restaurants**');
      expect(page.url()).toContain('restaurants');
    } else {
      // Try direct navigation
      await page.goto('http://localhost:3000/en/restaurants');
    }
  });

  test('should display restaurant list/dashboard', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Check for restaurant dashboard elements
    const createRestaurantButton = page.locator('button:has-text("Create Restaurant"), button:has-text("Add Restaurant"), button:has-text("New Restaurant")');
    const restaurantList = page.locator('[data-testid="restaurants-list"], .restaurants-list, table');
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    const dashboardCards = page.locator('.restaurant-card, .summary-card, .metric-card');
    
    // Should have at least one restaurant interface element
    const hasRestaurantInterface = await createRestaurantButton.first().isVisible() || 
                                  await restaurantList.first().isVisible() || 
                                  await searchInput.first().isVisible() ||
                                  await dashboardCards.first().isVisible();
    
    expect(hasRestaurantInterface).toBeTruthy();
  });

  test('should create a new restaurant', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Look for create restaurant button
    const createButton = page.locator('button:has-text("Create Restaurant"), button:has-text("Add Restaurant"), button:has-text("New Restaurant")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Fill restaurant creation form
      const nameInput = page.locator('input[name="name"], input[placeholder*="restaurant name"]');
      const descriptionInput = page.locator('textarea[name="description"], input[name="description"]');
      const addressInput = page.locator('input[name="address"], textarea[name="address"]');
      const phoneInput = page.locator('input[name="phone"], input[type="tel"]');
      const emailInput = page.locator('input[name="email"], input[type="email"]');
      const cuisineSelect = page.locator('select[name="cuisine"], select[name="cuisineType"]');
      
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Test Restaurant');
      }
      
      if (await descriptionInput.first().isVisible()) {
        await descriptionInput.first().fill('A test restaurant for validation');
      }
      
      if (await addressInput.first().isVisible()) {
        await addressInput.first().fill('123 Restaurant Street, Food City');
      }
      
      if (await phoneInput.first().isVisible()) {
        await phoneInput.first().fill('+1234567890');
      }
      
      if (await emailInput.first().isVisible()) {
        await emailInput.first().fill('<EMAIL>');
      }
      
      if (await cuisineSelect.first().isVisible()) {
        await cuisineSelect.first().selectOption({ index: 1 });
      }
      
      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show success message or redirect
        await page.waitForTimeout(2000);
      }
    }
  });

  test('should view restaurant details', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Look for restaurant details view
    const viewButton = page.locator('button:has-text("View"), a:has-text("View"), .view-button');
    const restaurantName = page.locator('.restaurant-name, [data-testid="restaurant-name"]');
    
    if (await viewButton.first().isVisible()) {
      await viewButton.first().click();
      
      // Should navigate to restaurant details
      await page.waitForTimeout(1000);
      
      // Check for details elements
      const detailsElements = page.locator('.restaurant-details, [data-testid="restaurant-details"]');
      const infoCards = page.locator('.info-card, .detail-card');
      const tabNavigation = page.locator('.tabs, [role="tablist"]');
      
      const hasDetails = await detailsElements.first().isVisible() || 
                        await infoCards.first().isVisible() ||
                        await tabNavigation.first().isVisible();
      
      expect(hasDetails).toBeTruthy();
    } else if (await restaurantName.first().isVisible()) {
      await restaurantName.first().click();
      
      // Should navigate to restaurant details
      await page.waitForTimeout(1000);
    }
  });

  test('should edit restaurant information', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Look for existing restaurant to edit
    const editButton = page.locator('button:has-text("Edit"), .edit-button, [data-testid="edit-button"]');
    
    if (await editButton.first().isVisible()) {
      await editButton.first().click();
      
      // Modify restaurant details
      const nameInput = page.locator('input[name="name"]');
      const descriptionInput = page.locator('textarea[name="description"]');
      
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Updated Test Restaurant');
      }
      
      if (await descriptionInput.first().isVisible()) {
        await descriptionInput.first().fill('Updated description for test restaurant');
      }
      
      const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")');
      if (await saveButton.first().isVisible()) {
        await saveButton.first().click();
      }
    }
  });

  test('should manage restaurant settings', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Navigate to restaurant details first
    const viewButton = page.locator('button:has-text("View"), .view-button');
    if (await viewButton.first().isVisible()) {
      await viewButton.first().click();
    }
    
    // Look for settings tab or button
    const settingsTab = page.locator('button:has-text("Settings"), a:has-text("Settings"), [data-testid="settings-tab"]');
    
    if (await settingsTab.first().isVisible()) {
      await settingsTab.first().click();
      
      // Check for settings interface
      const settingsForm = page.locator('form, .settings-form, [data-testid="settings-form"]');
      const operatingHours = page.locator('.operating-hours, [data-testid="operating-hours"]');
      const paymentSettings = page.locator('.payment-settings, [data-testid="payment-settings"]');
      
      const hasSettings = await settingsForm.first().isVisible() || 
                         await operatingHours.first().isVisible() ||
                         await paymentSettings.first().isVisible();
      
      expect(hasSettings).toBeTruthy();
    }
  });

  test('should manage restaurant staff', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Navigate to restaurant details
    const viewButton = page.locator('button:has-text("View"), .view-button');
    if (await viewButton.first().isVisible()) {
      await viewButton.first().click();
    }
    
    // Look for staff management
    const staffTab = page.locator('button:has-text("Staff"), a:has-text("Staff"), [data-testid="staff-tab"]');
    const addStaffButton = page.locator('button:has-text("Add Staff"), button:has-text("New Staff")');
    
    if (await staffTab.first().isVisible()) {
      await staffTab.first().click();
      
      // Check for staff management interface
      const staffList = page.locator('.staff-list, [data-testid="staff-list"], table');
      
      if (await addStaffButton.first().isVisible()) {
        await addStaffButton.first().click();
        
        // Fill staff form
        const nameInput = page.locator('input[name="staffName"], input[name="name"]');
        const roleSelect = page.locator('select[name="role"], select[name="position"]');
        const emailInput = page.locator('input[name="email"], input[type="email"]');
        
        if (await nameInput.first().isVisible()) {
          await nameInput.first().fill('Test Staff Member');
        }
        
        if (await roleSelect.first().isVisible()) {
          await roleSelect.first().selectOption({ index: 1 });
        }
        
        if (await emailInput.first().isVisible()) {
          await emailInput.first().fill('<EMAIL>');
        }
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Add")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should manage restaurant tables', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Navigate to restaurant details
    const viewButton = page.locator('button:has-text("View"), .view-button');
    if (await viewButton.first().isVisible()) {
      await viewButton.first().click();
    }
    
    // Look for table management
    const tablesTab = page.locator('button:has-text("Tables"), a:has-text("Tables"), [data-testid="tables-tab"]');
    const addTableButton = page.locator('button:has-text("Add Table"), button:has-text("New Table")');
    
    if (await tablesTab.first().isVisible()) {
      await tablesTab.first().click();
      
      if (await addTableButton.first().isVisible()) {
        await addTableButton.first().click();
        
        // Fill table form
        const tableNumberInput = page.locator('input[name="tableNumber"], input[name="number"]');
        const capacityInput = page.locator('input[name="capacity"], input[type="number"]');
        const locationInput = page.locator('input[name="location"], select[name="location"]');
        
        if (await tableNumberInput.first().isVisible()) {
          await tableNumberInput.first().fill('T001');
        }
        
        if (await capacityInput.first().isVisible()) {
          await capacityInput.first().fill('4');
        }
        
        if (await locationInput.first().isVisible()) {
          await locationInput.first().fill('Main Dining Area');
        }
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Add")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should handle restaurant status management', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Look for status management (open/closed, active/inactive)
    const statusToggle = page.locator('input[type="checkbox"], .toggle, .switch');
    const statusButton = page.locator('button:has-text("Open"), button:has-text("Closed"), button:has-text("Active")');
    
    if (await statusToggle.first().isVisible()) {
      await statusToggle.first().click();
    } else if (await statusButton.first().isVisible()) {
      await statusButton.first().click();
    }
  });

  test('should search and filter restaurants', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    if (await searchInput.first().isVisible()) {
      await searchInput.first().fill('test');
      await page.keyboard.press('Enter');
      
      // Wait for search results
      await page.waitForTimeout(1000);
    }
    
    // Test cuisine filter
    const cuisineFilter = page.locator('select[name="cuisine"], .cuisine-filter');
    
    if (await cuisineFilter.first().isVisible()) {
      await cuisineFilter.first().selectOption({ index: 1 });
    }
    
    // Test status filter
    const statusFilter = page.locator('select[name="status"], .status-filter');
    
    if (await statusFilter.first().isVisible()) {
      await statusFilter.first().selectOption({ index: 1 });
    }
  });

  test('should validate required fields in restaurant creation', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Try to create restaurant without required fields
    const createButton = page.locator('button:has-text("Create Restaurant"), button:has-text("Add Restaurant")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Submit without filling required fields
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show validation errors
        const errorMessages = page.locator('.error, [role="alert"], .text-red-500');
        await expect(errorMessages.first()).toBeVisible();
      }
    }
  });

  test('should handle restaurant image uploads', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants');
    
    // Navigate to restaurant creation or edit
    const createButton = page.locator('button:has-text("Create Restaurant"), button:has-text("Add Restaurant")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Look for image upload functionality
      const imageUpload = page.locator('input[type="file"], .image-upload');
      const addImageButton = page.locator('button:has-text("Add Image"), button:has-text("Upload Image")');
      
      if (await imageUpload.first().isVisible()) {
        // Test image upload (would need actual file in real test)
        // await imageUpload.first().setInputFiles('path/to/test-image.jpg');
      } else if (await addImageButton.first().isVisible()) {
        await addImageButton.first().click();
      }
    }
  });
});
