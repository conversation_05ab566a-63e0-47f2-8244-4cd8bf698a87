# End-to-End Test Suite

This directory contains comprehensive end-to-end tests for the ADC Account Web application, covering all major Linear card requirements.

## Test Coverage

### Authentication & Authorization (ADC-42, ADC-86)
**File:** `authentication.test.js`

✅ **Completed Tests:**
- Home page redirect to login
- Login form validation and display
- Registration form validation and display
- User registration flow
- User login with email/password
- Google OAuth authentication flow
- Session management and persistence
- Protected route access control
- Language switching functionality

**Status:** ✅ PASSING - Authentication system fixed and working

### Menu Management Flow (ADC-50)
**File:** `menu-management.test.js`

🔄 **Test Cases Created:**
- Navigation to menu management
- Menu creation and editing
- Menu category management
- Menu item creation and editing
- Item availability management
- Pricing validation and formatting
- Search and filtering functionality
- Menu publishing/unpublishing
- Image upload handling
- Required field validation

**Status:** 🔄 READY FOR TESTING - Tests created, needs backend implementation

### Organization & Branch Management (ADC-43, ADC-64, ADC-84, ADC-85)
**File:** `organization-management.test.js`

🔄 **Test Cases Created:**
- Organization creation and management
- Organization settings configuration
- Branch creation and management
- Branch dashboard and overview
- Permission and access control
- Status management (active/inactive)
- Search and filtering
- Required field validation

**Status:** 🔄 READY FOR TESTING - Tests created, needs backend implementation

## Test Results Summary

### ✅ Working Features
1. **Authentication System**
   - User registration with Supabase + NextAuth
   - Email/password login
   - Google OAuth integration
   - Session management
   - Route protection

2. **Basic Navigation**
   - Home page redirects
   - Language switching
   - Protected route handling

### 🔄 Pending Implementation
1. **Menu Management System**
   - Backend API endpoints
   - Database schema for menus/items
   - Frontend components

2. **Organization/Branch Management**
   - Backend API endpoints
   - Database schema for organizations/branches
   - Frontend components

3. **Restaurant Management**
   - Restaurant creation flow
   - Restaurant details management
   - Staff and table management

## Running the Tests

### Prerequisites
```bash
npm install @playwright/test
npx playwright install
```

### Running Tests
```bash
# Run all E2E tests
npx playwright test tests/e2e/

# Run specific test file
npx playwright test tests/e2e/authentication.test.js

# Run tests in headed mode (with browser UI)
npx playwright test --headed

# Run tests with debug mode
npx playwright test --debug
```

### Test Configuration
Create `playwright.config.js` in project root:
```javascript
module.exports = {
  testDir: './tests/e2e',
  timeout: 30000,
  use: {
    baseURL: 'http://localhost:3000',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
};
```

## Linear Card Status

| Card ID | Title | Status | Test File | Notes |
|---------|-------|--------|-----------|-------|
| ADC-42 | Authentication & Authorization Flow | ✅ Done | authentication.test.js | All tests passing |
| ADC-86 | Fix Authentication System | ✅ Done | authentication.test.js | NextAuth integration complete |
| ADC-50 | Menu Management Flow | 🔄 Ready | menu-management.test.js | Tests created, needs implementation |
| ADC-43 | User & Organization Management | 🔄 Ready | organization-management.test.js | Tests created, needs implementation |
| ADC-64 | Organization & Branch Management | 🔄 Ready | organization-management.test.js | Tests created, needs implementation |
| ADC-84 | Create New Branch Page | 🔄 Ready | organization-management.test.js | Tests created, needs implementation |
| ADC-85 | Branch Dashboard Page | 🔄 Ready | organization-management.test.js | Tests created, needs implementation |

## Next Steps

1. **Implement Backend APIs**
   - Menu management endpoints
   - Organization/branch management endpoints
   - Restaurant management endpoints

2. **Create Frontend Components**
   - Menu management UI
   - Organization/branch management UI
   - Restaurant management UI

3. **Run Full Test Suite**
   - Execute all E2E tests
   - Fix any failing tests
   - Update test cases as needed

4. **Additional Test Cases**
   - Invoice management (ADC-44)
   - Order processing (ADC-52)
   - Reservation management (ADC-53)
   - Staff management (ADC-55)
   - Table management (ADC-54)

## Test Environment Setup

### Database Setup
Ensure test database is properly configured with:
- User authentication tables
- Organization/branch schema
- Menu/restaurant schema
- Proper test data seeding

### Environment Variables
Required environment variables for testing:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## Troubleshooting

### Common Issues
1. **Authentication Tests Failing**
   - Check Supabase configuration
   - Verify NextAuth setup
   - Ensure test user exists

2. **Navigation Tests Failing**
   - Verify routes are properly configured
   - Check middleware setup
   - Ensure components are rendered

3. **Form Tests Failing**
   - Check form field selectors
   - Verify validation logic
   - Ensure proper error handling

### Debug Tips
- Use `--headed` flag to see browser actions
- Add `await page.pause()` to stop execution
- Use `console.log()` in tests for debugging
- Check browser console for JavaScript errors
