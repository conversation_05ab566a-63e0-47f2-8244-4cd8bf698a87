/**
 * End-to-End Menu Management Tests
 * Tests for Linear Card: ADC-50 - Test Validation: Menu Management Flow
 * 
 * This file tests the complete menu management functionality including:
 * - Menu creation and editing
 * - Menu item management
 * - Category organization
 * - Pricing and availability
 * - Menu publishing and status management
 */

const { test, expect } = require('@playwright/test');

test.describe('Menu Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/en/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard**');
  });

  test('should navigate to menu management page', async ({ page }) => {
    // Look for menu management link in navigation
    const menuLink = page.locator('a:has-text("Menu"), a:has-text("Menus"), button:has-text("Menu"), button:has-text("Menus")');
    
    if (await menuLink.first().isVisible()) {
      await menuLink.first().click();
      
      // Should navigate to menu page
      await page.waitForURL('**/menu**');
      expect(page.url()).toContain('menu');
    } else {
      // If no direct menu link, check if it's under a restaurant or shop section
      const restaurantLink = page.locator('a:has-text("Restaurant"), a:has-text("Shop"), button:has-text("Restaurant"), button:has-text("Shop")');
      
      if (await restaurantLink.first().isVisible()) {
        await restaurantLink.first().click();
        
        // Look for menu option in restaurant section
        const menuOption = page.locator('a:has-text("Menu"), button:has-text("Menu")');
        if (await menuOption.first().isVisible()) {
          await menuOption.first().click();
        }
      }
    }
  });

  test('should display menu management interface', async ({ page }) => {
    // Navigate to menu management (implementation depends on app structure)
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Check for key menu management elements
    const createMenuButton = page.locator('button:has-text("Create Menu"), button:has-text("Add Menu"), button:has-text("New Menu")');
    const menuList = page.locator('[data-testid="menu-list"], .menu-list, table');
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    // At least one of these should be visible
    const hasMenuInterface = await createMenuButton.first().isVisible() || 
                            await menuList.first().isVisible() || 
                            await searchInput.first().isVisible();
    
    expect(hasMenuInterface).toBeTruthy();
  });

  test('should create a new menu', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for create menu button
    const createButton = page.locator('button:has-text("Create Menu"), button:has-text("Add Menu"), button:has-text("New Menu")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Fill menu creation form
      const nameInput = page.locator('input[name="name"], input[placeholder*="name"], input[placeholder*="Name"]');
      const descriptionInput = page.locator('textarea[name="description"], input[name="description"]');
      
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Test Menu');
      }
      
      if (await descriptionInput.first().isVisible()) {
        await descriptionInput.first().fill('A test menu for validation');
      }
      
      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show success message or redirect
        await page.waitForTimeout(2000);
      }
    }
  });

  test('should manage menu categories', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for category management
    const categorySection = page.locator('[data-testid="categories"], .categories, .menu-categories');
    const addCategoryButton = page.locator('button:has-text("Add Category"), button:has-text("New Category")');
    
    if (await addCategoryButton.first().isVisible()) {
      await addCategoryButton.first().click();
      
      // Fill category form
      const categoryNameInput = page.locator('input[name="categoryName"], input[placeholder*="category"]');
      if (await categoryNameInput.first().isVisible()) {
        await categoryNameInput.first().fill('Appetizers');
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Add")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should add menu items', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for add item functionality
    const addItemButton = page.locator('button:has-text("Add Item"), button:has-text("New Item"), button:has-text("Add Menu Item")');
    
    if (await addItemButton.first().isVisible()) {
      await addItemButton.first().click();
      
      // Fill item form
      const itemNameInput = page.locator('input[name="itemName"], input[name="name"]');
      const priceInput = page.locator('input[name="price"], input[type="number"]');
      const descriptionInput = page.locator('textarea[name="description"]');
      
      if (await itemNameInput.first().isVisible()) {
        await itemNameInput.first().fill('Test Dish');
      }
      
      if (await priceInput.first().isVisible()) {
        await priceInput.first().fill('15.99');
      }
      
      if (await descriptionInput.first().isVisible()) {
        await descriptionInput.first().fill('A delicious test dish');
      }
      
      // Submit item
      const submitButton = page.locator('button[type="submit"], button:has-text("Add"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
      }
    }
  });

  test('should edit existing menu items', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for existing menu items
    const menuItems = page.locator('.menu-item, [data-testid="menu-item"], tr');
    const editButton = page.locator('button:has-text("Edit"), .edit-button, [data-testid="edit-button"]');
    
    if (await editButton.first().isVisible()) {
      await editButton.first().click();
      
      // Modify item details
      const nameInput = page.locator('input[name="name"], input[name="itemName"]');
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Updated Test Dish');
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should manage item availability', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for availability toggles
    const availabilityToggle = page.locator('input[type="checkbox"], .toggle, .switch');
    const availabilityButton = page.locator('button:has-text("Available"), button:has-text("Unavailable")');
    
    if (await availabilityToggle.first().isVisible()) {
      await availabilityToggle.first().click();
    } else if (await availabilityButton.first().isVisible()) {
      await availabilityButton.first().click();
    }
  });

  test('should handle menu pricing', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Test price validation and formatting
    const priceInput = page.locator('input[name="price"], input[type="number"]');
    
    if (await priceInput.first().isVisible()) {
      // Test invalid price
      await priceInput.first().fill('-5');
      await page.keyboard.press('Tab');
      
      // Should show validation error
      const errorMessage = page.locator('.error, [role="alert"], .text-red-500');
      
      // Test valid price
      await priceInput.first().fill('12.50');
      await page.keyboard.press('Tab');
    }
  });

  test('should search and filter menu items', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    if (await searchInput.first().isVisible()) {
      await searchInput.first().fill('test');
      await page.keyboard.press('Enter');
      
      // Wait for search results
      await page.waitForTimeout(1000);
    }
    
    // Test category filter
    const categoryFilter = page.locator('select[name="category"], .category-filter');
    
    if (await categoryFilter.first().isVisible()) {
      await categoryFilter.first().selectOption({ index: 1 });
    }
  });

  test('should publish and unpublish menu', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for publish/unpublish functionality
    const publishButton = page.locator('button:has-text("Publish"), button:has-text("Unpublish")');
    const statusToggle = page.locator('.status-toggle, input[name="published"]');
    
    if (await publishButton.first().isVisible()) {
      await publishButton.first().click();
      
      // Should show confirmation or status change
      await page.waitForTimeout(1000);
    } else if (await statusToggle.first().isVisible()) {
      await statusToggle.first().click();
    }
  });

  test('should handle menu item images', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Look for image upload functionality
    const imageUpload = page.locator('input[type="file"], .image-upload');
    const addImageButton = page.locator('button:has-text("Add Image"), button:has-text("Upload Image")');
    
    if (await imageUpload.first().isVisible()) {
      // Test image upload (would need actual file in real test)
      // await imageUpload.first().setInputFiles('path/to/test-image.jpg');
    } else if (await addImageButton.first().isVisible()) {
      await addImageButton.first().click();
    }
  });

  test('should validate required fields', async ({ page }) => {
    await page.goto('http://localhost:3000/en/restaurants/menu');
    
    // Try to create item without required fields
    const addItemButton = page.locator('button:has-text("Add Item"), button:has-text("New Item")');
    
    if (await addItemButton.first().isVisible()) {
      await addItemButton.first().click();
      
      // Submit without filling required fields
      const submitButton = page.locator('button[type="submit"], button:has-text("Add"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show validation errors
        const errorMessages = page.locator('.error, [role="alert"], .text-red-500');
        await expect(errorMessages.first()).toBeVisible();
      }
    }
  });
});
