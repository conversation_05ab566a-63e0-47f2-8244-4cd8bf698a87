/**
 * End-to-End Authentication Tests
 * Tests for Linear Cards: ADC-42, ADC-86
 *
 * This file tests the complete authentication flow including:
 * - User registration
 * - User login with email/password
 * - Google OAuth authentication
 * - Session management
 * - Authentication redirects
 */

const { test, expect } = require('@playwright/test');

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto('http://localhost:3000');
  });

  test('should redirect from home to login page', async ({ page }) => {
    // Wait for redirect
    await page.waitForURL('**/login**');

    // Verify we're on the login page
    expect(page.url()).toContain('/login');

    // Check for login form elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should display login form with all required elements', async ({ page }) => {
    await page.goto('http://localhost:3000/en/login');

    // Check form elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    // Check Google OAuth button
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();

    // Check sign up link
    await expect(page.locator('a:has-text("Sign up")')).toBeVisible();
  });

  test('should show validation error for invalid credentials', async ({ page }) => {
    await page.goto('http://localhost:3000/en/login');

    // Fill invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');

    // Submit form
    await page.click('button[type="submit"]');

    // Wait for error message
    await expect(page.locator('[role="alert"], .error, .text-red-500, .text-destructive')).toBeVisible();
  });

  test('should navigate to registration page', async ({ page }) => {
    await page.goto('http://localhost:3000/en/login');

    // Click sign up link
    await page.click('a:has-text("Sign up")');

    // Verify we're on registration page
    await page.waitForURL('**/register**');
    expect(page.url()).toContain('/register');
  });

  test('should display registration form with all required fields', async ({ page }) => {
    await page.goto('http://localhost:3000/en/register');

    // Check form elements
    await expect(page.locator('input[name="name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();

    // Check sign in link
    await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
  });

  test('should validate password confirmation', async ({ page }) => {
    await page.goto('http://localhost:3000/en/register');

    // Fill form with mismatched passwords
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.fill('input[name="confirmPassword"]', 'differentpassword');

    // Submit form
    await page.click('button[type="submit"]');

    // Should show password mismatch error
    // Note: This depends on client-side validation implementation
  });

  test('should successfully register a new user', async ({ page }) => {
    await page.goto('http://localhost:3000/en/register');

    const timestamp = Date.now();
    const email = `test${timestamp}@example.com`;

    // Fill registration form
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', email);
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.fill('input[name="confirmPassword"]', 'TestPassword123!');

    // Submit form
    await page.click('button[type="submit"]');

    // Should redirect to login page with success message
    await page.waitForURL('**/login**');
    expect(page.url()).toContain('/login');
    expect(page.url()).toContain('message=Account created successfully');
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // First register a user
    await page.goto('http://localhost:3000/en/register');

    const timestamp = Date.now();
    const email = `logintest${timestamp}@example.com`;
    const password = 'TestPassword123!';

    await page.fill('input[name="name"]', 'Login Test User');
    await page.fill('input[name="email"]', email);
    await page.fill('input[name="password"]', password);
    await page.fill('input[name="confirmPassword"]', password);
    await page.click('button[type="submit"]');

    // Wait for redirect to login
    await page.waitForURL('**/login**');

    // Now login with the created user
    await page.fill('input[type="email"]', email);
    await page.fill('input[type="password"]', password);
    await page.click('button[type="submit"]');

    // Should redirect to dashboard
    await page.waitForURL('**/dashboard**');
    expect(page.url()).toContain('/dashboard');
  });

  test.skip('should initiate Google OAuth flow', async ({ page }) => {
    // Skipping OAuth test as requested
    await page.goto('http://localhost:3000/en/login');

    // Click Google OAuth button
    const googleButton = page.locator('button:has-text("Continue with Google")');
    await expect(googleButton).toBeVisible();

    // Click and wait for navigation to Google
    await googleButton.click();

    // Should redirect to Google OAuth
    await page.waitForURL('**/accounts.google.com/**');
    expect(page.url()).toContain('accounts.google.com');
  });

  test('should protect authenticated routes', async ({ page }) => {
    // Try to access dashboard without authentication
    await page.goto('http://localhost:3000/en/dashboard');

    // Should redirect to login
    await page.waitForURL('**/login**');
    expect(page.url()).toContain('/login');
    expect(page.url()).toContain('callbackUrl=%2Fen%2Fdashboard');
  });

  test('should handle language switching', async ({ page }) => {
    await page.goto('http://localhost:3000/en/login');

    // Check for language switcher
    const languageButton = page.locator('button:has-text("English")');
    if (await languageButton.isVisible()) {
      await languageButton.click();

      // Check if Thai option is available
      const thaiOption = page.locator('text=ไทย');
      if (await thaiOption.isVisible()) {
        await thaiOption.click();

        // Should redirect to Thai version
        await page.waitForURL('**/th/login**');
        expect(page.url()).toContain('/th/login');
      }
    }
  });
});

test.describe('Session Management', () => {
  test('should maintain session across page reloads', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/en/login');

    // Use existing test user credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');

    // Wait for dashboard
    await page.waitForURL('**/dashboard**');

    // Reload page
    await page.reload();

    // Should still be on dashboard (session maintained)
    expect(page.url()).toContain('/dashboard');
  });

  test('should redirect authenticated users away from auth pages', async ({ page }) => {
    // Login first
    await page.goto('http://localhost:3000/en/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard**');

    // Try to access login page while authenticated
    await page.goto('http://localhost:3000/en/login');

    // Should redirect to dashboard
    await page.waitForURL('**/dashboard**');
    expect(page.url()).toContain('/dashboard');
  });
});
