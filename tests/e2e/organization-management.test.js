/**
 * End-to-End Organization & Branch Management Tests
 * Tests for Linear Cards: ADC-43, ADC-64, ADC-84, ADC-85
 * 
 * This file tests the complete organization and branch management functionality including:
 * - Organization creation and management
 * - Branch creation and management
 * - User permissions and access control
 * - Organization settings and configuration
 * - Branch dashboard and overview
 */

const { test, expect } = require('@playwright/test');

test.describe('Organization Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/en/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard**');
  });

  test('should navigate to organizations page', async ({ page }) => {
    // Look for organizations link in navigation
    const orgLink = page.locator('a:has-text("Organizations"), button:has-text("Organizations")');
    
    if (await orgLink.first().isVisible()) {
      await orgLink.first().click();
      await page.waitForURL('**/organizations**');
      expect(page.url()).toContain('organizations');
    } else {
      // Try direct navigation
      await page.goto('http://localhost:3000/en/organizations');
    }
  });

  test('should display organizations list', async ({ page }) => {
    await page.goto('http://localhost:3000/en/organizations');
    
    // Check for organizations interface elements
    const createOrgButton = page.locator('button:has-text("Create Organization"), button:has-text("Add Organization"), button:has-text("New Organization")');
    const orgList = page.locator('[data-testid="organizations-list"], .organizations-list, table');
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    // Should have at least one organization interface element
    const hasOrgInterface = await createOrgButton.first().isVisible() || 
                           await orgList.first().isVisible() || 
                           await searchInput.first().isVisible();
    
    expect(hasOrgInterface).toBeTruthy();
  });

  test('should create a new organization', async ({ page }) => {
    await page.goto('http://localhost:3000/en/organizations');
    
    // Look for create organization button
    const createButton = page.locator('button:has-text("Create Organization"), button:has-text("Add Organization"), button:has-text("New Organization")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Fill organization creation form
      const nameInput = page.locator('input[name="name"], input[placeholder*="organization name"]');
      const descriptionInput = page.locator('textarea[name="description"], input[name="description"]');
      const emailInput = page.locator('input[name="email"], input[type="email"]');
      const phoneInput = page.locator('input[name="phone"], input[type="tel"]');
      
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Test Organization');
      }
      
      if (await descriptionInput.first().isVisible()) {
        await descriptionInput.first().fill('A test organization for validation');
      }
      
      if (await emailInput.first().isVisible()) {
        await emailInput.first().fill('<EMAIL>');
      }
      
      if (await phoneInput.first().isVisible()) {
        await phoneInput.first().fill('+1234567890');
      }
      
      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show success message or redirect
        await page.waitForTimeout(2000);
      }
    }
  });

  test('should edit organization details', async ({ page }) => {
    await page.goto('http://localhost:3000/en/organizations');
    
    // Look for existing organization to edit
    const editButton = page.locator('button:has-text("Edit"), .edit-button, [data-testid="edit-button"]');
    const orgRow = page.locator('.organization-row, [data-testid="organization-item"], tr');
    
    if (await editButton.first().isVisible()) {
      await editButton.first().click();
      
      // Modify organization details
      const nameInput = page.locator('input[name="name"]');
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Updated Test Organization');
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should manage organization settings', async ({ page }) => {
    await page.goto('http://localhost:3000/en/organizations');
    
    // Look for settings or configuration option
    const settingsButton = page.locator('button:has-text("Settings"), a:has-text("Settings"), .settings-button');
    
    if (await settingsButton.first().isVisible()) {
      await settingsButton.first().click();
      
      // Check for settings interface
      const settingsForm = page.locator('form, .settings-form, [data-testid="settings-form"]');
      await expect(settingsForm.first()).toBeVisible();
    }
  });
});

test.describe('Branch Management Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('http://localhost:3000/en/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/dashboard**');
  });

  test('should navigate to branches page', async ({ page }) => {
    // Look for branches link in navigation
    const branchLink = page.locator('a:has-text("Branches"), button:has-text("Branches")');
    
    if (await branchLink.first().isVisible()) {
      await branchLink.first().click();
      await page.waitForURL('**/branches**');
      expect(page.url()).toContain('branches');
    } else {
      // Try direct navigation
      await page.goto('http://localhost:3000/en/branches');
    }
  });

  test('should display branches list', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Check for branches interface elements
    const createBranchButton = page.locator('button:has-text("Create Branch"), button:has-text("Add Branch"), button:has-text("New Branch")');
    const branchList = page.locator('[data-testid="branches-list"], .branches-list, table');
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    // Should have at least one branch interface element
    const hasBranchInterface = await createBranchButton.first().isVisible() || 
                              await branchList.first().isVisible() || 
                              await searchInput.first().isVisible();
    
    expect(hasBranchInterface).toBeTruthy();
  });

  test('should create a new branch', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Look for create branch button
    const createButton = page.locator('button:has-text("Create Branch"), button:has-text("Add Branch"), button:has-text("New Branch")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Fill branch creation form
      const nameInput = page.locator('input[name="name"], input[placeholder*="branch name"]');
      const addressInput = page.locator('input[name="address"], textarea[name="address"]');
      const cityInput = page.locator('input[name="city"]');
      const phoneInput = page.locator('input[name="phone"], input[type="tel"]');
      const managerInput = page.locator('input[name="manager"], select[name="manager"]');
      
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Test Branch');
      }
      
      if (await addressInput.first().isVisible()) {
        await addressInput.first().fill('123 Test Street');
      }
      
      if (await cityInput.first().isVisible()) {
        await cityInput.first().fill('Test City');
      }
      
      if (await phoneInput.first().isVisible()) {
        await phoneInput.first().fill('+1234567890');
      }
      
      // Submit form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show success message or redirect
        await page.waitForTimeout(2000);
      }
    }
  });

  test('should view branch dashboard', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Look for branch dashboard or details view
    const viewButton = page.locator('button:has-text("View"), a:has-text("View"), .view-button');
    const branchName = page.locator('.branch-name, [data-testid="branch-name"]');
    
    if (await viewButton.first().isVisible()) {
      await viewButton.first().click();
      
      // Should navigate to branch dashboard
      await page.waitForTimeout(1000);
      
      // Check for dashboard elements
      const dashboardElements = page.locator('.dashboard, [data-testid="dashboard"], .branch-overview');
      const statsCards = page.locator('.stat-card, .metric-card, .summary-card');
      
      const hasDashboard = await dashboardElements.first().isVisible() || 
                          await statsCards.first().isVisible();
      
      expect(hasDashboard).toBeTruthy();
    } else if (await branchName.first().isVisible()) {
      await branchName.first().click();
      
      // Should navigate to branch details
      await page.waitForTimeout(1000);
    }
  });

  test('should manage branch permissions', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Look for permissions or users management
    const permissionsButton = page.locator('button:has-text("Permissions"), button:has-text("Users"), a:has-text("Permissions")');
    
    if (await permissionsButton.first().isVisible()) {
      await permissionsButton.first().click();
      
      // Check for permissions interface
      const permissionsForm = page.locator('.permissions-form, [data-testid="permissions"], .user-management');
      const userList = page.locator('.user-list, table');
      
      const hasPermissionsInterface = await permissionsForm.first().isVisible() || 
                                     await userList.first().isVisible();
      
      expect(hasPermissionsInterface).toBeTruthy();
    }
  });

  test('should edit branch details', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Look for existing branch to edit
    const editButton = page.locator('button:has-text("Edit"), .edit-button, [data-testid="edit-button"]');
    
    if (await editButton.first().isVisible()) {
      await editButton.first().click();
      
      // Modify branch details
      const nameInput = page.locator('input[name="name"]');
      if (await nameInput.first().isVisible()) {
        await nameInput.first().fill('Updated Test Branch');
        
        const saveButton = page.locator('button:has-text("Save"), button:has-text("Update")');
        if (await saveButton.first().isVisible()) {
          await saveButton.first().click();
        }
      }
    }
  });

  test('should handle branch status management', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Look for status management (active/inactive)
    const statusToggle = page.locator('input[type="checkbox"], .toggle, .switch');
    const statusButton = page.locator('button:has-text("Active"), button:has-text("Inactive")');
    
    if (await statusToggle.first().isVisible()) {
      await statusToggle.first().click();
    } else if (await statusButton.first().isVisible()) {
      await statusButton.first().click();
    }
  });

  test('should search and filter branches', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Test search functionality
    const searchInput = page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
    
    if (await searchInput.first().isVisible()) {
      await searchInput.first().fill('test');
      await page.keyboard.press('Enter');
      
      // Wait for search results
      await page.waitForTimeout(1000);
    }
    
    // Test status filter
    const statusFilter = page.locator('select[name="status"], .status-filter');
    
    if (await statusFilter.first().isVisible()) {
      await statusFilter.first().selectOption({ index: 1 });
    }
  });

  test('should validate required fields in branch creation', async ({ page }) => {
    await page.goto('http://localhost:3000/en/branches');
    
    // Try to create branch without required fields
    const createButton = page.locator('button:has-text("Create Branch"), button:has-text("Add Branch")');
    
    if (await createButton.first().isVisible()) {
      await createButton.first().click();
      
      // Submit without filling required fields
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.first().isVisible()) {
        await submitButton.first().click();
        
        // Should show validation errors
        const errorMessages = page.locator('.error, [role="alert"], .text-red-500');
        await expect(errorMessages.first()).toBeVisible();
      }
    }
  });
});
