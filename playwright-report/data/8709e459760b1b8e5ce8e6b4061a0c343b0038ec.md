# Test info

- Name: Authentication Flow >> should successfully login with valid credentials
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/authentication.test.js:125:3

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/login**" until "load"
============================================================
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/authentication.test.js:140:16
```

# Page snapshot

```yaml
- banner:
  - link "ADC Account":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "Reports":
      - /url: /reports
  - button "Toggle theme"
  - button "🇺🇸 English"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Create Account Enter your details below to create an account Name (Optional)
  - textbox "Name (Optional)": Login Test User
  - text: Email
  - textbox "Email": <EMAIL>
  - text: Password
  - textbox "Password": TestPassword123!
  - paragraph: Password must be at least 8 characters long and include a number and a special character
  - text: Confirm Password
  - textbox "Confirm Password": TestPassword123!
  - button "Sign Up"
  - text: Already have an account?
  - link "Sign In":
    - /url: /en/en/login
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
   40 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   41 |
   42 |     // Check Google OAuth button
   43 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
   44 |
   45 |     // Check sign up link
   46 |     await expect(page.locator('a:has-text("Sign up")')).toBeVisible();
   47 |   });
   48 |
   49 |   test('should show validation error for invalid credentials', async ({ page }) => {
   50 |     await page.goto('http://localhost:3000/en/login');
   51 |
   52 |     // Fill invalid credentials
   53 |     await page.fill('input[type="email"]', '<EMAIL>');
   54 |     await page.fill('input[type="password"]', 'wrongpassword');
   55 |
   56 |     // Submit form
   57 |     await page.click('button[type="submit"]');
   58 |
   59 |     // Wait for error message
   60 |     await expect(page.locator('[role="alert"], .error, .text-red-500, .text-destructive')).toBeVisible();
   61 |   });
   62 |
   63 |   test('should navigate to registration page', async ({ page }) => {
   64 |     await page.goto('http://localhost:3000/en/login');
   65 |
   66 |     // Click sign up link
   67 |     await page.click('a:has-text("Sign up")');
   68 |
   69 |     // Verify we're on registration page
   70 |     await page.waitForURL('**/register**');
   71 |     expect(page.url()).toContain('/register');
   72 |   });
   73 |
   74 |   test('should display registration form with all required fields', async ({ page }) => {
   75 |     await page.goto('http://localhost:3000/en/register');
   76 |
   77 |     // Check form elements
   78 |     await expect(page.locator('input[name="name"]')).toBeVisible();
   79 |     await expect(page.locator('input[name="email"]')).toBeVisible();
   80 |     await expect(page.locator('input[name="password"]')).toBeVisible();
   81 |     await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
   82 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   83 |
   84 |     // Check sign in link
   85 |     await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
   86 |   });
   87 |
   88 |   test('should validate password confirmation', async ({ page }) => {
   89 |     await page.goto('http://localhost:3000/en/register');
   90 |
   91 |     // Fill form with mismatched passwords
   92 |     await page.fill('input[name="name"]', 'Test User');
   93 |     await page.fill('input[name="email"]', '<EMAIL>');
   94 |     await page.fill('input[name="password"]', 'password123');
   95 |     await page.fill('input[name="confirmPassword"]', 'differentpassword');
   96 |
   97 |     // Submit form
   98 |     await page.click('button[type="submit"]');
   99 |
  100 |     // Should show password mismatch error
  101 |     // Note: This depends on client-side validation implementation
  102 |   });
  103 |
  104 |   test('should successfully register a new user', async ({ page }) => {
  105 |     await page.goto('http://localhost:3000/en/register');
  106 |
  107 |     const timestamp = Date.now();
  108 |     const email = `test${timestamp}@example.com`;
  109 |
  110 |     // Fill registration form
  111 |     await page.fill('input[name="name"]', 'Test User');
  112 |     await page.fill('input[name="email"]', email);
  113 |     await page.fill('input[name="password"]', 'TestPassword123!');
  114 |     await page.fill('input[name="confirmPassword"]', 'TestPassword123!');
  115 |
  116 |     // Submit form
  117 |     await page.click('button[type="submit"]');
  118 |
  119 |     // Should redirect to login page with success message
  120 |     await page.waitForURL('**/login**');
  121 |     expect(page.url()).toContain('/login');
  122 |     expect(page.url()).toContain('message=Account created successfully');
  123 |   });
  124 |
  125 |   test('should successfully login with valid credentials', async ({ page }) => {
  126 |     // First register a user
  127 |     await page.goto('http://localhost:3000/en/register');
  128 |
  129 |     const timestamp = Date.now();
  130 |     const email = `logintest${timestamp}@example.com`;
  131 |     const password = 'TestPassword123!';
  132 |
  133 |     await page.fill('input[name="name"]', 'Login Test User');
  134 |     await page.fill('input[name="email"]', email);
  135 |     await page.fill('input[name="password"]', password);
  136 |     await page.fill('input[name="confirmPassword"]', password);
  137 |     await page.click('button[type="submit"]');
  138 |
  139 |     // Wait for redirect to login
> 140 |     await page.waitForURL('**/login**');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  141 |
  142 |     // Now login with the created user
  143 |     await page.fill('input[type="email"]', email);
  144 |     await page.fill('input[type="password"]', password);
  145 |     await page.click('button[type="submit"]');
  146 |
  147 |     // Should redirect to dashboard
  148 |     await page.waitForURL('**/dashboard**');
  149 |     expect(page.url()).toContain('/dashboard');
  150 |   });
  151 |
  152 |   test.skip('should initiate Google OAuth flow', async ({ page }) => {
  153 |     // Skipping OAuth test as requested
  154 |     await page.goto('http://localhost:3000/en/login');
  155 |
  156 |     // Click Google OAuth button
  157 |     const googleButton = page.locator('button:has-text("Continue with Google")');
  158 |     await expect(googleButton).toBeVisible();
  159 |
  160 |     // Click and wait for navigation to Google
  161 |     await googleButton.click();
  162 |
  163 |     // Should redirect to Google OAuth
  164 |     await page.waitForURL('**/accounts.google.com/**');
  165 |     expect(page.url()).toContain('accounts.google.com');
  166 |   });
  167 |
  168 |   test('should protect authenticated routes', async ({ page }) => {
  169 |     // Try to access dashboard without authentication
  170 |     await page.goto('http://localhost:3000/en/dashboard');
  171 |
  172 |     // Should redirect to login
  173 |     await page.waitForURL('**/login**');
  174 |     expect(page.url()).toContain('/login');
  175 |     expect(page.url()).toContain('callbackUrl=%2Fen%2Fdashboard');
  176 |   });
  177 |
  178 |   test('should handle language switching', async ({ page }) => {
  179 |     await page.goto('http://localhost:3000/en/login');
  180 |
  181 |     // Check for language switcher
  182 |     const languageButton = page.locator('button:has-text("English")');
  183 |     if (await languageButton.isVisible()) {
  184 |       await languageButton.click();
  185 |
  186 |       // Check if Thai option is available
  187 |       const thaiOption = page.locator('text=ไทย');
  188 |       if (await thaiOption.isVisible()) {
  189 |         await thaiOption.click();
  190 |
  191 |         // Should redirect to Thai version
  192 |         await page.waitForURL('**/th/login**');
  193 |         expect(page.url()).toContain('/th/login');
  194 |       }
  195 |     }
  196 |   });
  197 | });
  198 |
  199 | test.describe('Session Management', () => {
  200 |   test('should maintain session across page reloads', async ({ page }) => {
  201 |     // Login first
  202 |     await page.goto('http://localhost:3000/en/login');
  203 |
  204 |     // Use existing test user credentials
  205 |     await page.fill('input[type="email"]', '<EMAIL>');
  206 |     await page.fill('input[type="password"]', 'TestPassword123!');
  207 |     await page.click('button[type="submit"]');
  208 |
  209 |     // Wait for dashboard
  210 |     await page.waitForURL('**/dashboard**');
  211 |
  212 |     // Reload page
  213 |     await page.reload();
  214 |
  215 |     // Should still be on dashboard (session maintained)
  216 |     expect(page.url()).toContain('/dashboard');
  217 |   });
  218 |
  219 |   test('should redirect authenticated users away from auth pages', async ({ page }) => {
  220 |     // Login first
  221 |     await page.goto('http://localhost:3000/en/login');
  222 |     await page.fill('input[type="email"]', '<EMAIL>');
  223 |     await page.fill('input[type="password"]', 'TestPassword123!');
  224 |     await page.click('button[type="submit"]');
  225 |     await page.waitForURL('**/dashboard**');
  226 |
  227 |     // Try to access login page while authenticated
  228 |     await page.goto('http://localhost:3000/en/login');
  229 |
  230 |     // Should redirect to dashboard
  231 |     await page.waitForURL('**/dashboard**');
  232 |     expect(page.url()).toContain('/dashboard');
  233 |   });
  234 | });
  235 |
```