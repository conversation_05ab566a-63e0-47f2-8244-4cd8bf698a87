package services

import "gorm.io/gorm"

// This file contains stub implementations for all services
// Each service will be implemented in separate files

// Organization Services - Implementation moved to organization_service.go

// BranchService - Implementation moved to branch_service.go

// MerchantService - Implementation moved to merchant_service.go

// Financial Services
// InvoiceService - Implementation moved to invoice_service.go

// BillService - Implementation moved to bill_service.go

// ExpenseService - Implementation moved to expense_service.go

// CustomerService - Implementation moved to customer_service.go

// VendorService - Implementation moved to vendor_service.go

// Accounting Services
// AccountService - Implementation moved to account_service.go

// JournalEntryService - Implementation moved to journal_entry_service.go

// TaxRateService - Implementation moved to tax_rate_service.go

// Banking Services
// BankAccountService - Implementation moved to bank_account_service.go

// BankTransactionService - Implementation moved to bank_transaction_service.go

// BankReconciliationService - Implementation moved to bank_reconciliation_service.go

// Asset Services - Implementation moved to asset_service.go

// Inventory Services - Implementation moved to inventory_service.go

// Employee and Payroll Services - Implementation moved to payroll_service.go

// Credit and Collection Services
// CreditNoteService - Implementation moved to credit_note_service.go

// CustomerCreditService - Implementation moved to customer_credit_service.go

// PaymentReminderService - Implementation moved to payment_reminder_service.go

// CustomerStatementService - Implementation moved to customer_statement_service.go

// CollectionCaseService - Implementation moved to collection_case_service.go

// CollectionActivityService - Implementation moved to collection_activity_service.go

// CollectionTemplateService - Implementation moved to collection_template_service.go

// Template Services
// InvoiceTemplateService - Implementation moved to invoice_template_service.go

// RecurringInvoiceService - Implementation moved to recurring_invoice_service.go

// BudgetTemplateService - Implementation moved to budget_template_service.go

// EmailTemplateService - Implementation moved to email_template_service.go

// Budget and Cash Flow Services
// BudgetService - Implementation moved to budget_service.go

type CashFlowCategoryService struct{ db *gorm.DB }

func NewCashFlowCategoryService(db *gorm.DB) *CashFlowCategoryService {
	return &CashFlowCategoryService{db: db}
}

type CashFlowItemService struct{ db *gorm.DB }

func NewCashFlowItemService(db *gorm.DB) *CashFlowItemService { return &CashFlowItemService{db: db} }

type RecurringCashFlowItemService struct{ db *gorm.DB }

func NewRecurringCashFlowItemService(db *gorm.DB) *RecurringCashFlowItemService {
	return &RecurringCashFlowItemService{db: db}
}

// Email Services
// EmailLogService - Implementation moved to email_log_service.go

// Order Services
// SalesOrderService - Implementation moved to sales_order_service.go

// Tax Services
// TaxReportService - Implementation moved to tax_report_service.go

// Subscription Services
type SubscriptionService struct{ db *gorm.DB }

func NewSubscriptionService(db *gorm.DB) *SubscriptionService { return &SubscriptionService{db: db} }

type UsageRecordService struct{ db *gorm.DB }

func NewUsageRecordService(db *gorm.DB) *UsageRecordService { return &UsageRecordService{db: db} }

// API Services
type ApiKeyService struct{ db *gorm.DB }

func NewApiKeyService(db *gorm.DB) *ApiKeyService { return &ApiKeyService{db: db} }

// Audit Services
type AuditLogService struct{ db *gorm.DB }

func NewAuditLogService(db *gorm.DB) *AuditLogService { return &AuditLogService{db: db} }
