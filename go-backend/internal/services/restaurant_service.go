package services

import (
	"errors"
	"fmt"

	"github.com/adc-account/go-backend/internal/models"
	"gorm.io/gorm"
)

type RestaurantService struct {
	db *gorm.DB
}

func NewRestaurantService(db *gorm.DB) *RestaurantService {
	return &RestaurantService{db: db}
}

// Restaurant CRUD operations
func (s *RestaurantService) CreateRestaurant(restaurant *models.Restaurant) error {
	return s.db.Create(restaurant).Error
}

func (s *RestaurantService) GetRestaurantByID(id string) (*models.Restaurant, error) {
	var restaurant models.Restaurant
	err := s.db.Preload("Menus").Preload("Tables").Preload("Staff").First(&restaurant, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &restaurant, nil
}

func (s *RestaurantService) GetRestaurantsByMerchantID(merchantID string) ([]models.Restaurant, error) {
	var restaurants []models.Restaurant
	err := s.db.Where("merchant_id = ? AND is_active = ?", merchantID, true).Find(&restaurants).Error
	return restaurants, err
}

func (s *RestaurantService) UpdateRestaurant(restaurant *models.Restaurant) error {
	return s.db.Save(restaurant).Error
}

func (s *RestaurantService) DeleteRestaurant(id string) error {
	return s.db.Model(&models.Restaurant{}).Where("id = ?", id).Update("is_active", false).Error
}

// Menu CRUD operations
func (s *RestaurantService) CreateMenu(menu *models.Menu) error {
	return s.db.Create(menu).Error
}

func (s *RestaurantService) GetMenuByID(id string) (*models.Menu, error) {
	var menu models.Menu
	err := s.db.Preload("Categories").Preload("Items").First(&menu, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &menu, nil
}

func (s *RestaurantService) GetMenusByRestaurantID(restaurantID string) ([]models.Menu, error) {
	var menus []models.Menu
	err := s.db.Where("restaurant_id = ? AND is_active = ?", restaurantID, true).
		Order("display_order ASC").Find(&menus).Error
	return menus, err
}

func (s *RestaurantService) UpdateMenu(menu *models.Menu) error {
	return s.db.Save(menu).Error
}

func (s *RestaurantService) DeleteMenu(id string) error {
	return s.db.Model(&models.Menu{}).Where("id = ?", id).Update("is_active", false).Error
}

func (s *RestaurantService) PublishMenu(id string) error {
	return s.db.Model(&models.Menu{}).Where("id = ?", id).Update("is_published", true).Error
}

func (s *RestaurantService) UnpublishMenu(id string) error {
	return s.db.Model(&models.Menu{}).Where("id = ?", id).Update("is_published", false).Error
}

// MenuCategory CRUD operations
func (s *RestaurantService) CreateMenuCategory(category *models.MenuCategory) error {
	return s.db.Create(category).Error
}

func (s *RestaurantService) GetMenuCategoriesByMenuID(menuID string) ([]models.MenuCategory, error) {
	var categories []models.MenuCategory
	err := s.db.Where("menu_id = ? AND is_active = ?", menuID, true).
		Order("display_order ASC").Find(&categories).Error
	return categories, err
}

func (s *RestaurantService) UpdateMenuCategory(category *models.MenuCategory) error {
	return s.db.Save(category).Error
}

func (s *RestaurantService) DeleteMenuCategory(id string) error {
	return s.db.Model(&models.MenuCategory{}).Where("id = ?", id).Update("is_active", false).Error
}

// MenuItem CRUD operations
func (s *RestaurantService) CreateMenuItem(item *models.MenuItem) error {
	return s.db.Create(item).Error
}

func (s *RestaurantService) GetMenuItemByID(id string) (*models.MenuItem, error) {
	var item models.MenuItem
	err := s.db.Preload("Category").First(&item, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (s *RestaurantService) GetMenuItemsByMenuID(menuID string) ([]models.MenuItem, error) {
	var items []models.MenuItem
	err := s.db.Where("menu_id = ? AND is_active = ?", menuID, true).
		Order("display_order ASC").Find(&items).Error
	return items, err
}

func (s *RestaurantService) GetMenuItemsByCategoryID(categoryID string) ([]models.MenuItem, error) {
	var items []models.MenuItem
	err := s.db.Where("category_id = ? AND is_active = ?", categoryID, true).
		Order("display_order ASC").Find(&items).Error
	return items, err
}

func (s *RestaurantService) UpdateMenuItem(item *models.MenuItem) error {
	return s.db.Save(item).Error
}

func (s *RestaurantService) DeleteMenuItem(id string) error {
	return s.db.Model(&models.MenuItem{}).Where("id = ?", id).Update("is_active", false).Error
}

func (s *RestaurantService) SetMenuItemAvailability(id string, available bool) error {
	return s.db.Model(&models.MenuItem{}).Where("id = ?", id).Update("is_available", available).Error
}

// RestaurantTable CRUD operations
func (s *RestaurantService) CreateRestaurantTable(table *models.RestaurantTable) error {
	// Check for duplicate table number in the same restaurant
	var count int64
	s.db.Model(&models.RestaurantTable{}).
		Where("restaurant_id = ? AND table_number = ? AND is_active = ?", table.RestaurantID, table.TableNumber, true).
		Count(&count)
	
	if count > 0 {
		return errors.New("table number already exists in this restaurant")
	}
	
	return s.db.Create(table).Error
}

func (s *RestaurantService) GetRestaurantTablesByRestaurantID(restaurantID string) ([]models.RestaurantTable, error) {
	var tables []models.RestaurantTable
	err := s.db.Where("restaurant_id = ? AND is_active = ?", restaurantID, true).
		Order("table_number ASC").Find(&tables).Error
	return tables, err
}

func (s *RestaurantService) UpdateRestaurantTable(table *models.RestaurantTable) error {
	return s.db.Save(table).Error
}

func (s *RestaurantService) DeleteRestaurantTable(id string) error {
	return s.db.Model(&models.RestaurantTable{}).Where("id = ?", id).Update("is_active", false).Error
}

// RestaurantStaff CRUD operations
func (s *RestaurantService) CreateRestaurantStaff(staff *models.RestaurantStaff) error {
	return s.db.Create(staff).Error
}

func (s *RestaurantService) GetRestaurantStaffByRestaurantID(restaurantID string) ([]models.RestaurantStaff, error) {
	var staff []models.RestaurantStaff
	err := s.db.Where("restaurant_id = ? AND is_active = ?", restaurantID, true).
		Preload("User").Find(&staff).Error
	return staff, err
}

func (s *RestaurantService) UpdateRestaurantStaff(staff *models.RestaurantStaff) error {
	return s.db.Save(staff).Error
}

func (s *RestaurantService) DeleteRestaurantStaff(id string) error {
	return s.db.Model(&models.RestaurantStaff{}).Where("id = ?", id).Update("is_active", false).Error
}

// RestaurantOrder CRUD operations
func (s *RestaurantService) CreateRestaurantOrder(order *models.RestaurantOrder) error {
	// Generate order number if not provided
	if order.OrderNumber == "" {
		var count int64
		s.db.Model(&models.RestaurantOrder{}).
			Where("restaurant_id = ? AND DATE(created_at) = CURRENT_DATE", order.RestaurantID).
			Count(&count)
		order.OrderNumber = fmt.Sprintf("ORD-%d", count+1)
	}
	
	return s.db.Create(order).Error
}

func (s *RestaurantService) GetRestaurantOrderByID(id string) (*models.RestaurantOrder, error) {
	var order models.RestaurantOrder
	err := s.db.Preload("Items").Preload("Items.MenuItem").
		Preload("Table").Preload("Staff").First(&order, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (s *RestaurantService) GetRestaurantOrdersByRestaurantID(restaurantID string) ([]models.RestaurantOrder, error) {
	var orders []models.RestaurantOrder
	err := s.db.Where("restaurant_id = ?", restaurantID).
		Order("created_at DESC").Find(&orders).Error
	return orders, err
}

func (s *RestaurantService) UpdateRestaurantOrder(order *models.RestaurantOrder) error {
	return s.db.Save(order).Error
}

func (s *RestaurantService) UpdateOrderStatus(id string, status models.RestaurantOrderStatus) error {
	return s.db.Model(&models.RestaurantOrder{}).Where("id = ?", id).Update("status", status).Error
}

// RestaurantOrderItem CRUD operations
func (s *RestaurantService) CreateRestaurantOrderItem(item *models.RestaurantOrderItem) error {
	return s.db.Create(item).Error
}

func (s *RestaurantService) GetRestaurantOrderItemsByOrderID(orderID string) ([]models.RestaurantOrderItem, error) {
	var items []models.RestaurantOrderItem
	err := s.db.Where("order_id = ?", orderID).
		Preload("MenuItem").Find(&items).Error
	return items, err
}

func (s *RestaurantService) UpdateRestaurantOrderItem(item *models.RestaurantOrderItem) error {
	return s.db.Save(item).Error
}

func (s *RestaurantService) DeleteRestaurantOrderItem(id string) error {
	return s.db.Delete(&models.RestaurantOrderItem{}, "id = ?", id).Error
}

// Search and filter operations
func (s *RestaurantService) SearchMenuItems(restaurantID, query string) ([]models.MenuItem, error) {
	var items []models.MenuItem
	err := s.db.Where("menu_id IN (SELECT id FROM menus WHERE restaurant_id = ?) AND is_active = ? AND (name ILIKE ? OR description ILIKE ?)", 
		restaurantID, true, "%"+query+"%", "%"+query+"%").
		Order("display_order ASC").Find(&items).Error
	return items, err
}
