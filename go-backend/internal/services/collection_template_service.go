package services

import (
	"fmt"

	"adc-account-backend/internal/models"

	"gorm.io/gorm"
)

// CollectionTemplateService handles collection template business logic
type CollectionTemplateService struct {
	db *gorm.DB
}

// NewCollectionTemplateService creates a new CollectionTemplateService
func NewCollectionTemplateService(db *gorm.DB) *CollectionTemplateService {
	return &CollectionTemplateService{db: db}
}

// GetAllCollectionTemplates retrieves all collection templates with pagination
func (s *CollectionTemplateService) GetAllCollectionTemplates(page, limit int, search string) ([]models.CollectionTemplate, int64, error) {
	var templates []models.CollectionTemplate
	var total int64

	query := s.db.Model(&models.CollectionTemplate{})

	// Apply search filter
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection templates: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Steps").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection templates: %w", err)
	}

	return templates, total, nil
}

// GetCollectionTemplateByID retrieves a collection template by ID
func (s *CollectionTemplateService) GetCollectionTemplateByID(id string) (*models.CollectionTemplate, error) {
	var template models.CollectionTemplate
	if err := s.db.Preload("Merchant").Preload("Steps").
		Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection template not found")
		}
		return nil, fmt.Errorf("failed to fetch collection template: %w", err)
	}
	return &template, nil
}

// GetCollectionTemplatesByMerchant retrieves collection templates for a specific merchant
func (s *CollectionTemplateService) GetCollectionTemplatesByMerchant(merchantID string, page, limit int, search string, isActive *bool) ([]models.CollectionTemplate, int64, error) {
	var templates []models.CollectionTemplate
	var total int64

	query := s.db.Model(&models.CollectionTemplate{}).Where("merchant_id = ?", merchantID)

	// Apply search filter
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply active filter
	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection templates: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Merchant").Preload("Steps").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection templates: %w", err)
	}

	return templates, total, nil
}

// CreateCollectionTemplate creates a new collection template
func (s *CollectionTemplateService) CreateCollectionTemplate(template *models.CollectionTemplate) error {
	// Validate the collection template
	if err := s.ValidateCollectionTemplate(template); err != nil {
		return err
	}

	// Set default values
	if template.IsActive == false && len(template.Steps) == 0 {
		template.IsActive = true
	}

	// Create the collection template with steps in a transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Create the collection template
		if err := tx.Create(template).Error; err != nil {
			return fmt.Errorf("failed to create collection template: %w", err)
		}

		// Update step foreign keys
		for i := range template.Steps {
			template.Steps[i].TemplateID = template.ID
		}

		// Create steps if provided
		if len(template.Steps) > 0 {
			if err := tx.Create(&template.Steps).Error; err != nil {
				return fmt.Errorf("failed to create collection template steps: %w", err)
			}
		}

		return nil
	})
}

// UpdateCollectionTemplate updates an existing collection template
func (s *CollectionTemplateService) UpdateCollectionTemplate(id string, updates *models.CollectionTemplate) (*models.CollectionTemplate, error) {
	// Check if collection template exists
	var existingTemplate models.CollectionTemplate
	if err := s.db.Where("id = ?", id).First(&existingTemplate).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection template not found")
		}
		return nil, fmt.Errorf("failed to fetch collection template: %w", err)
	}

	// Validate updates
	if err := s.ValidateCollectionTemplateUpdates(updates); err != nil {
		return nil, err
	}

	// Update the collection template in a transaction
	return s.updateCollectionTemplateTransaction(id, updates)
}

// DeleteCollectionTemplate deletes a collection template
func (s *CollectionTemplateService) DeleteCollectionTemplate(id string) error {
	// Check if collection template exists
	var template models.CollectionTemplate
	if err := s.db.Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("collection template not found")
		}
		return fmt.Errorf("failed to fetch collection template: %w", err)
	}

	// Delete in transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete related steps first
		if err := tx.Where("template_id = ?", id).Delete(&models.CollectionTemplateStep{}).Error; err != nil {
			return fmt.Errorf("failed to delete collection template steps: %w", err)
		}

		// Delete the collection template
		if err := tx.Delete(&template).Error; err != nil {
			return fmt.Errorf("failed to delete collection template: %w", err)
		}

		return nil
	})
}

// GetActiveCollectionTemplates gets active collection templates for a merchant
func (s *CollectionTemplateService) GetActiveCollectionTemplates(merchantID string) ([]models.CollectionTemplate, error) {
	var templates []models.CollectionTemplate

	if err := s.db.Preload("Merchant").Preload("Steps").
		Where("merchant_id = ? AND is_active = ?", merchantID, true).
		Order("name ASC").Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch active collection templates: %w", err)
	}

	return templates, nil
}

// GetCollectionTemplateSteps gets steps for a specific template
func (s *CollectionTemplateService) GetCollectionTemplateSteps(templateID string) ([]models.CollectionTemplateStep, error) {
	var steps []models.CollectionTemplateStep

	if err := s.db.Preload("Template").
		Where("template_id = ?", templateID).
		Order("step_number ASC").Find(&steps).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch collection template steps: %w", err)
	}

	return steps, nil
}

// CreateCollectionTemplateStep creates a new step for a template
func (s *CollectionTemplateService) CreateCollectionTemplateStep(step *models.CollectionTemplateStep) error {
	// Validate the step
	if err := s.ValidateCollectionTemplateStep(step); err != nil {
		return err
	}

	// Auto-assign step number if not provided
	if step.StepNumber == 0 {
		var maxStepNumber int
		s.db.Model(&models.CollectionTemplateStep{}).
			Where("template_id = ?", step.TemplateID).
			Select("COALESCE(MAX(step_number), 0)").Scan(&maxStepNumber)
		step.StepNumber = maxStepNumber + 1
	}

	// Create the step
	if err := s.db.Create(step).Error; err != nil {
		return fmt.Errorf("failed to create collection template step: %w", err)
	}

	return nil
}

// UpdateCollectionTemplateStep updates an existing template step
func (s *CollectionTemplateService) UpdateCollectionTemplateStep(id string, updates *models.CollectionTemplateStep) (*models.CollectionTemplateStep, error) {
	// Check if step exists
	var existingStep models.CollectionTemplateStep
	if err := s.db.Where("id = ?", id).First(&existingStep).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection template step not found")
		}
		return nil, fmt.Errorf("failed to fetch collection template step: %w", err)
	}

	// Validate updates
	if err := s.ValidateCollectionTemplateStepUpdates(updates); err != nil {
		return nil, err
	}

	// Update the step
	if err := s.db.Model(&existingStep).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update collection template step: %w", err)
	}

	// Fetch and return updated step
	var updatedStep models.CollectionTemplateStep
	if err := s.db.Preload("Template").Where("id = ?", id).First(&updatedStep).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated collection template step: %w", err)
	}

	return &updatedStep, nil
}

// DeleteCollectionTemplateStep deletes a template step
func (s *CollectionTemplateService) DeleteCollectionTemplateStep(id string) error {
	// Check if step exists
	var step models.CollectionTemplateStep
	if err := s.db.Where("id = ?", id).First(&step).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("collection template step not found")
		}
		return fmt.Errorf("failed to fetch collection template step: %w", err)
	}

	// Delete the step
	if err := s.db.Delete(&step).Error; err != nil {
		return fmt.Errorf("failed to delete collection template step: %w", err)
	}

	return nil
}

// updateCollectionTemplateTransaction updates collection template in a transaction
func (s *CollectionTemplateService) updateCollectionTemplateTransaction(id string, updates *models.CollectionTemplate) (*models.CollectionTemplate, error) {
	var updatedTemplate models.CollectionTemplate

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Update the collection template
		if err := tx.Model(&models.CollectionTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update collection template: %w", err)
		}

		// If steps are provided, update them
		if len(updates.Steps) > 0 {
			// Delete existing steps
			if err := tx.Where("template_id = ?", id).Delete(&models.CollectionTemplateStep{}).Error; err != nil {
				return fmt.Errorf("failed to delete existing steps: %w", err)
			}

			// Create new steps
			for i := range updates.Steps {
				updates.Steps[i].TemplateID = id
			}
			if err := tx.Create(&updates.Steps).Error; err != nil {
				return fmt.Errorf("failed to create new steps: %w", err)
			}
		}

		// Fetch updated template
		if err := tx.Preload("Merchant").Preload("Steps").
			Where("id = ?", id).First(&updatedTemplate).Error; err != nil {
			return fmt.Errorf("failed to fetch updated collection template: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &updatedTemplate, nil
}

// ValidateCollectionTemplate validates collection template data
func (s *CollectionTemplateService) ValidateCollectionTemplate(template *models.CollectionTemplate) error {
	if template.MerchantID == "" {
		return fmt.Errorf("merchant ID is required")
	}
	if template.Name == "" {
		return fmt.Errorf("name is required")
	}

	// Validate steps if provided
	for i, step := range template.Steps {
		if err := s.ValidateCollectionTemplateStep(&step); err != nil {
			return fmt.Errorf("step %d: %w", i+1, err)
		}
	}

	return nil
}

// ValidateCollectionTemplateUpdates validates collection template update data
func (s *CollectionTemplateService) ValidateCollectionTemplateUpdates(updates *models.CollectionTemplate) error {
	if updates.Name != "" && len(updates.Name) == 0 {
		return fmt.Errorf("name cannot be empty")
	}

	// Validate steps if provided
	for i, step := range updates.Steps {
		if err := s.ValidateCollectionTemplateStep(&step); err != nil {
			return fmt.Errorf("step %d: %w", i+1, err)
		}
	}

	return nil
}

// ValidateCollectionTemplateStep validates collection template step data
func (s *CollectionTemplateService) ValidateCollectionTemplateStep(step *models.CollectionTemplateStep) error {
	if step.Name == "" {
		return fmt.Errorf("step name is required")
	}
	if step.Type == "" {
		return fmt.Errorf("step type is required")
	}
	if step.DaysAfterDue < 0 {
		return fmt.Errorf("days after due cannot be negative")
	}

	return nil
}

// ValidateCollectionTemplateStepUpdates validates collection template step update data
func (s *CollectionTemplateService) ValidateCollectionTemplateStepUpdates(updates *models.CollectionTemplateStep) error {
	if updates.Name != "" && len(updates.Name) == 0 {
		return fmt.Errorf("step name cannot be empty")
	}
	if updates.DaysAfterDue < 0 {
		return fmt.Errorf("days after due cannot be negative")
	}

	return nil
}
