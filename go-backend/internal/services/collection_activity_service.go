package services

import (
	"fmt"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// CollectionActivityService handles collection activity business logic
type CollectionActivityService struct {
	db *gorm.DB
}

// NewCollectionActivityService creates a new CollectionActivityService
func NewCollectionActivityService(db *gorm.DB) *CollectionActivityService {
	return &CollectionActivityService{db: db}
}

// GetAllCollectionActivities retrieves all collection activities with pagination
func (s *CollectionActivityService) GetAllCollectionActivities(page, limit int, search string) ([]models.CollectionActivity, int64, error) {
	var activities []models.CollectionActivity
	var total int64

	query := s.db.Model(&models.CollectionActivity{})

	// Apply search filter
	if search != "" {
		query = query.Where("description ILIKE ? OR outcome ILIKE ? OR next_action ILIKE ?", 
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection activities: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("CollectionCase").Preload("Creator").
		Offset(offset).Limit(limit).Order("date DESC").Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection activities: %w", err)
	}

	return activities, total, nil
}

// GetCollectionActivityByID retrieves a collection activity by ID
func (s *CollectionActivityService) GetCollectionActivityByID(id string) (*models.CollectionActivity, error) {
	var activity models.CollectionActivity
	if err := s.db.Preload("CollectionCase").Preload("Creator").
		Where("id = ?", id).First(&activity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection activity not found")
		}
		return nil, fmt.Errorf("failed to fetch collection activity: %w", err)
	}
	return &activity, nil
}

// GetCollectionActivitiesByCase retrieves collection activities for a specific case
func (s *CollectionActivityService) GetCollectionActivitiesByCase(caseID string, page, limit int, activityType *string) ([]models.CollectionActivity, int64, error) {
	var activities []models.CollectionActivity
	var total int64

	query := s.db.Model(&models.CollectionActivity{}).Where("collection_case_id = ?", caseID)

	// Apply activity type filter
	if activityType != nil && *activityType != "" {
		query = query.Where("type = ?", *activityType)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection activities: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("CollectionCase").Preload("Creator").
		Offset(offset).Limit(limit).Order("date DESC").Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection activities: %w", err)
	}

	return activities, total, nil
}

// GetCollectionActivitiesByCreator retrieves collection activities created by a specific user
func (s *CollectionActivityService) GetCollectionActivitiesByCreator(creatorID string, page, limit int) ([]models.CollectionActivity, int64, error) {
	var activities []models.CollectionActivity
	var total int64

	query := s.db.Model(&models.CollectionActivity{}).Where("created_by = ?", creatorID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count collection activities: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("CollectionCase").Preload("Creator").
		Offset(offset).Limit(limit).Order("date DESC").Find(&activities).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch collection activities: %w", err)
	}

	return activities, total, nil
}

// CreateCollectionActivity creates a new collection activity
func (s *CollectionActivityService) CreateCollectionActivity(activity *models.CollectionActivity) error {
	// Validate the collection activity
	if err := s.ValidateCollectionActivity(activity); err != nil {
		return err
	}

	// Set default date if not provided
	if activity.Date.IsZero() {
		activity.Date = time.Now()
	}

	// Create the collection activity
	if err := s.db.Create(activity).Error; err != nil {
		return fmt.Errorf("failed to create collection activity: %w", err)
	}

	// Update collection case last contact date if this is a contact activity
	if s.isContactActivity(activity.Type) {
		if err := s.updateCaseLastContactDate(activity.CollectionCaseID, activity.Date); err != nil {
			// Log error but don't fail the creation
			fmt.Printf("Warning: failed to update case last contact date: %v\n", err)
		}
	}

	// If this is a payment activity, update the collection case collected amount
	if activity.Type == models.CollectionActivityTypePayment && activity.Amount != nil {
		if err := s.updateCaseCollectedAmount(activity.CollectionCaseID, *activity.Amount); err != nil {
			// Log error but don't fail the creation
			fmt.Printf("Warning: failed to update case collected amount: %v\n", err)
		}
	}

	return nil
}

// UpdateCollectionActivity updates an existing collection activity
func (s *CollectionActivityService) UpdateCollectionActivity(id string, updates *models.CollectionActivity) (*models.CollectionActivity, error) {
	// Check if collection activity exists
	var existingActivity models.CollectionActivity
	if err := s.db.Where("id = ?", id).First(&existingActivity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("collection activity not found")
		}
		return nil, fmt.Errorf("failed to fetch collection activity: %w", err)
	}

	// Validate updates
	if err := s.ValidateCollectionActivityUpdates(updates); err != nil {
		return nil, err
	}

	// Update the collection activity
	if err := s.db.Model(&existingActivity).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update collection activity: %w", err)
	}

	// Fetch and return updated collection activity
	return s.GetCollectionActivityByID(id)
}

// DeleteCollectionActivity deletes a collection activity
func (s *CollectionActivityService) DeleteCollectionActivity(id string) error {
	// Check if collection activity exists
	var activity models.CollectionActivity
	if err := s.db.Where("id = ?", id).First(&activity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("collection activity not found")
		}
		return fmt.Errorf("failed to fetch collection activity: %w", err)
	}

	// Delete the collection activity
	if err := s.db.Delete(&activity).Error; err != nil {
		return fmt.Errorf("failed to delete collection activity: %w", err)
	}

	return nil
}

// GetActivitySummary gets summary statistics for collection activities
func (s *CollectionActivityService) GetActivitySummary(caseID *string, creatorID *string) (*ActivitySummary, error) {
	query := s.db.Model(&models.CollectionActivity{})

	// Apply filters
	if caseID != nil {
		query = query.Where("collection_case_id = ?", *caseID)
	}
	if creatorID != nil {
		query = query.Where("created_by = ?", *creatorID)
	}

	var summary ActivitySummary

	// Get total activities count
	if err := query.Count(&summary.TotalActivities).Error; err != nil {
		return nil, fmt.Errorf("failed to count total activities: %w", err)
	}

	// Get activities by type
	var typeCounts []struct {
		Type  models.CollectionActivityType `json:"type"`
		Count int64                         `json:"count"`
	}
	if err := query.Select("type, COUNT(*) as count").Group("type").Find(&typeCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count activities by type: %w", err)
	}

	summary.ActivitiesByType = make(map[string]int64)
	for _, tc := range typeCounts {
		summary.ActivitiesByType[string(tc.Type)] = tc.Count
	}

	// Get total payment amount
	var totalPayment decimal.Decimal
	if err := query.Where("type = ? AND amount IS NOT NULL", models.CollectionActivityTypePayment).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalPayment).Error; err != nil {
		return nil, fmt.Errorf("failed to sum payment amounts: %w", err)
	}
	summary.TotalPaymentAmount = totalPayment

	// Get activities in last 30 days
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	if err := query.Where("date >= ?", thirtyDaysAgo).Count(&summary.ActivitiesLast30Days).Error; err != nil {
		return nil, fmt.Errorf("failed to count recent activities: %w", err)
	}

	return &summary, nil
}

// isContactActivity checks if an activity type is a contact activity
func (s *CollectionActivityService) isContactActivity(activityType models.CollectionActivityType) bool {
	contactTypes := []models.CollectionActivityType{
		models.CollectionActivityTypeCall,
		models.CollectionActivityTypeEmail,
		models.CollectionActivityTypeLetter,
		models.CollectionActivityTypeMeeting,
	}

	for _, ct := range contactTypes {
		if activityType == ct {
			return true
		}
	}
	return false
}

// updateCaseLastContactDate updates the last contact date for a collection case
func (s *CollectionActivityService) updateCaseLastContactDate(caseID string, contactDate time.Time) error {
	return s.db.Model(&models.CollectionCase{}).
		Where("id = ?", caseID).
		Update("last_contact_date", contactDate).Error
}

// updateCaseCollectedAmount updates the collected amount for a collection case
func (s *CollectionActivityService) updateCaseCollectedAmount(caseID string, paymentAmount decimal.Decimal) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get current case
		var collectionCase models.CollectionCase
		if err := tx.Where("id = ?", caseID).First(&collectionCase).Error; err != nil {
			return err
		}

		// Update collected amount and recalculate balance
		newCollectedAmount := collectionCase.CollectedAmount.Add(paymentAmount)
		newBalanceAmount := collectionCase.TotalAmount.Sub(newCollectedAmount)

		return tx.Model(&collectionCase).Updates(map[string]interface{}{
			"collected_amount": newCollectedAmount,
			"balance_amount":   newBalanceAmount,
		}).Error
	})
}

// ValidateCollectionActivity validates collection activity data
func (s *CollectionActivityService) ValidateCollectionActivity(activity *models.CollectionActivity) error {
	if activity.CollectionCaseID == "" {
		return fmt.Errorf("collection case ID is required")
	}
	if activity.Type == "" {
		return fmt.Errorf("activity type is required")
	}
	if activity.Description == "" {
		return fmt.Errorf("description is required")
	}
	if activity.CreatedBy == "" {
		return fmt.Errorf("created by is required")
	}
	if activity.Amount != nil && activity.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("amount cannot be negative")
	}

	return nil
}

// ValidateCollectionActivityUpdates validates collection activity update data
func (s *CollectionActivityService) ValidateCollectionActivityUpdates(updates *models.CollectionActivity) error {
	if updates.Amount != nil && updates.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("amount cannot be negative")
	}

	return nil
}

// ActivitySummary represents summary statistics for collection activities
type ActivitySummary struct {
	TotalActivities       int64                  `json:"totalActivities"`
	ActivitiesByType      map[string]int64       `json:"activitiesByType"`
	TotalPaymentAmount    decimal.Decimal        `json:"totalPaymentAmount"`
	ActivitiesLast30Days  int64                  `json:"activitiesLast30Days"`
}
