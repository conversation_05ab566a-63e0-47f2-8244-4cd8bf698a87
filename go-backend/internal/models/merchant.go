package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Merchant represents the merchants table
type Merchant struct {
	ID                  string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	Name                string    `json:"name" gorm:"type:varchar(255);not null"`
	Address             *string   `json:"address" gorm:"type:text"`
	Phone               *string   `json:"phone" gorm:"type:varchar(50)"`
	PrimaryContactEmail *string   `json:"primaryContactEmail" gorm:"type:varchar(255)"`
	CreatedAt           time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt           time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
	Currency            string    `json:"currency" gorm:"type:varchar(10);default:'USD'"`
	FiscalYearStart     *string   `json:"fiscalYearStart" gorm:"type:varchar(10)"`
	LegalName           *string   `json:"legalName" gorm:"type:varchar(255)"`
	LogoURL             *string   `json:"logoUrl" gorm:"type:text"`
	TaxID               *string   `json:"taxId" gorm:"type:varchar(100)"`
	Website             *string   `json:"website" gorm:"type:varchar(255)"`
	StripeCustomerID    *string   `json:"stripeCustomerId" gorm:"type:varchar(255)"`

	// Relationships
	ApiKeys                []ApiKey                 `json:"apiKeys" gorm:"foreignKey:MerchantID"`
	Assets                 []Asset                  `json:"assets" gorm:"foreignKey:MerchantID"`
	AuditLogs              []AuditLog               `json:"auditLogs" gorm:"foreignKey:MerchantID"`
	BankAccounts           []BankAccount            `json:"bankAccounts" gorm:"foreignKey:MerchantID"`
	BankReconciliations    []BankReconciliation     `json:"bankReconciliations" gorm:"foreignKey:MerchantID"`
	Bills                  []Bill                   `json:"bills" gorm:"foreignKey:MerchantID"`
	BudgetItems            []BudgetItem             `json:"budgetItems" gorm:"foreignKey:MerchantID"`
	BudgetTemplates        []BudgetTemplate         `json:"budgetTemplates" gorm:"foreignKey:MerchantID"`
	CashFlowCategories     []CashFlowCategory       `json:"cashFlowCategories" gorm:"foreignKey:MerchantID"`
	CashFlowItems          []CashFlowItem           `json:"cashFlowItems" gorm:"foreignKey:MerchantID"`
	ChartOfAccounts        []ChartOfAccount         `json:"chartOfAccounts" gorm:"foreignKey:MerchantID"`
	CollectionCases        []CollectionCase         `json:"collectionCases" gorm:"foreignKey:MerchantID"`
	CollectionTemplates    []CollectionTemplate     `json:"collectionTemplates" gorm:"foreignKey:MerchantID"`
	CreditNotes            []CreditNote             `json:"creditNotes" gorm:"foreignKey:MerchantID"`
	Customers              []Customer               `json:"customers" gorm:"foreignKey:MerchantID"`
	CustomerStatements     []CustomerStatement      `json:"customerStatements" gorm:"foreignKey:MerchantID"`
	EmailLogs              []EmailLog               `json:"emailLogs" gorm:"foreignKey:MerchantID"`
	EmailTemplates         []EmailTemplate          `json:"emailTemplates" gorm:"foreignKey:MerchantID"`
	Employees              []Employee               `json:"employees" gorm:"foreignKey:MerchantID"`
	Expenses               []Expense                `json:"expenses" gorm:"foreignKey:MerchantID"`
	InventoryItems         []InventoryItem          `json:"inventoryItems" gorm:"foreignKey:MerchantID"`
	InventoryTransactions  []InventoryTransaction   `json:"inventoryTransactions" gorm:"foreignKey:MerchantID"`
	Invoices               []Invoice                `json:"invoices" gorm:"foreignKey:MerchantID"`
	InvoiceTemplates       []InvoiceTemplate        `json:"invoiceTemplates" gorm:"foreignKey:MerchantID"`
	JournalEntries         []JournalEntry           `json:"journalEntries" gorm:"foreignKey:MerchantID"`
	PaymentReminders       []PaymentReminder        `json:"paymentReminders" gorm:"foreignKey:MerchantID"`
	PayrollDetails         []PayrollDetail          `json:"payrollDetails" gorm:"foreignKey:MerchantID"`
	PayrollRuns            []PayrollRun             `json:"payrollRuns" gorm:"foreignKey:MerchantID"`
	RecurringCashFlowItems []RecurringCashFlowItem  `json:"recurringCashFlowItems" gorm:"foreignKey:MerchantID"`
	RecurringInvoices      []RecurringInvoice       `json:"recurringInvoices" gorm:"foreignKey:MerchantID"`
	SalesOrders            []SalesOrder             `json:"salesOrders" gorm:"foreignKey:MerchantID"`
	Subscription           *Subscription            `json:"subscription" gorm:"foreignKey:MerchantID"`
	TaxRates               []TaxRate                `json:"taxRates" gorm:"foreignKey:MerchantID"`
	TaxReports             []TaxReport              `json:"taxReports" gorm:"foreignKey:MerchantID"`
	UserPermissions        []UserMerchantPermission `json:"userPermissions" gorm:"foreignKey:MerchantID"`
	Vendors                []Vendor                 `json:"vendors" gorm:"foreignKey:MerchantID"`
	Users                  []User                   `json:"users" gorm:"many2many:user_merchants"`
	PersonalUser           *User                    `json:"personalUser" gorm:"foreignKey:PersonalMerchantID"`
	Restaurants            []Restaurant             `json:"restaurants" gorm:"foreignKey:MerchantID"`
}

// BeforeCreate hook for Merchant
func (m *Merchant) BeforeCreate(tx *gorm.DB) error {
	if m.ID == "" {
		m.ID = uuid.New().String()
	}
	return nil
}

// ApiKey represents the api_keys table
type ApiKey struct {
	ID         string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID string     `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name       string     `json:"name" gorm:"type:varchar(255);not null"`
	Prefix     string     `json:"prefix" gorm:"type:varchar(50);not null;index"`
	Key        *string    `json:"key" gorm:"type:varchar(255)"`
	ExpiresAt  *time.Time `json:"expiresAt"`
	LastUsed   *time.Time `json:"lastUsed"`
	Scopes     []string   `json:"scopes" gorm:"type:text[]"`
	IsActive   bool       `json:"isActive" gorm:"default:true;index"`
	CreatedAt  time.Time  `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Merchant Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
}

// BeforeCreate hook for ApiKey
func (a *ApiKey) BeforeCreate(tx *gorm.DB) error {
	if a.ID == "" {
		a.ID = uuid.New().String()
	}
	return nil
}

// AuditLog represents the audit_logs table
type AuditLog struct {
	ID         string    `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID *string   `json:"merchantId" gorm:"type:varchar(36);index"`
	UserID     *string   `json:"userId" gorm:"type:varchar(36);index"`
	Action     string    `json:"action" gorm:"type:varchar(255);not null;index"`
	Resource   string    `json:"resource" gorm:"type:varchar(255);not null;index"`
	ResourceID *string   `json:"resourceId" gorm:"type:varchar(36)"`
	Details    *string   `json:"details" gorm:"type:jsonb"`
	IPAddress  *string   `json:"ipAddress" gorm:"type:varchar(45)"`
	UserAgent  *string   `json:"userAgent" gorm:"type:text"`
	Path       string    `json:"path" gorm:"type:varchar(500);not null;index"`
	Method     string    `json:"method" gorm:"type:varchar(10);not null"`
	StatusCode int       `json:"statusCode" gorm:"not null"`
	Timestamp  time.Time `json:"timestamp" gorm:"autoCreateTime;index"`

	// Relationships
	Merchant *Merchant `json:"merchant" gorm:"foreignKey:MerchantID"`
	User     *User     `json:"user" gorm:"foreignKey:UserID"`
}

// BeforeCreate hook for AuditLog
func (a *AuditLog) BeforeCreate(tx *gorm.DB) error {
	if a.ID == "" {
		a.ID = uuid.New().String()
	}
	return nil
}
