package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Restaurant represents the restaurants table
type Restaurant struct {
	ID          string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MerchantID  string     `json:"merchantId" gorm:"type:varchar(36);not null;index"`
	Name        string     `json:"name" gorm:"type:varchar(255);not null"`
	Description *string    `json:"description" gorm:"type:text"`
	Address     *string    `json:"address" gorm:"type:text"`
	Phone       *string    `json:"phone" gorm:"type:varchar(50)"`
	Email       *string    `json:"email" gorm:"type:varchar(255)"`
	Website     *string    `json:"website" gorm:"type:varchar(255)"`
	CuisineType *string    `json:"cuisineType" gorm:"type:varchar(100)"`
	LogoURL     *string    `json:"logoUrl" gorm:"type:text"`
	IsActive    bool       `json:"isActive" gorm:"default:true;index"`
	CreatedAt   time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Merchant Merchant           `json:"merchant" gorm:"foreignKey:MerchantID"`
	Menus    []Menu             `json:"menus" gorm:"foreignKey:RestaurantID"`
	Tables   []RestaurantTable  `json:"tables" gorm:"foreignKey:RestaurantID"`
	Staff    []RestaurantStaff  `json:"staff" gorm:"foreignKey:RestaurantID"`
	Orders   []RestaurantOrder  `json:"orders" gorm:"foreignKey:RestaurantID"`
}

// BeforeCreate hook for Restaurant
func (r *Restaurant) BeforeCreate(tx *gorm.DB) error {
	if r.ID == "" {
		r.ID = uuid.New().String()
	}
	return nil
}

// Menu represents the menus table
type Menu struct {
	ID           string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	RestaurantID string     `json:"restaurantId" gorm:"type:varchar(36);not null;index"`
	Name         string     `json:"name" gorm:"type:varchar(255);not null"`
	Description  *string    `json:"description" gorm:"type:text"`
	IsActive     bool       `json:"isActive" gorm:"default:true;index"`
	IsPublished  bool       `json:"isPublished" gorm:"default:false;index"`
	DisplayOrder int        `json:"displayOrder" gorm:"default:0"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Restaurant Restaurant     `json:"restaurant" gorm:"foreignKey:RestaurantID"`
	Categories []MenuCategory `json:"categories" gorm:"foreignKey:MenuID"`
	Items      []MenuItem     `json:"items" gorm:"foreignKey:MenuID"`
}

// BeforeCreate hook for Menu
func (m *Menu) BeforeCreate(tx *gorm.DB) error {
	if m.ID == "" {
		m.ID = uuid.New().String()
	}
	return nil
}

// MenuCategory represents the menu_categories table
type MenuCategory struct {
	ID           string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MenuID       string     `json:"menuId" gorm:"type:varchar(36);not null;index"`
	Name         string     `json:"name" gorm:"type:varchar(255);not null"`
	Description  *string    `json:"description" gorm:"type:text"`
	DisplayOrder int        `json:"displayOrder" gorm:"default:0;index"`
	IsActive     bool       `json:"isActive" gorm:"default:true;index"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Menu  Menu       `json:"menu" gorm:"foreignKey:MenuID"`
	Items []MenuItem `json:"items" gorm:"foreignKey:CategoryID"`
}

// BeforeCreate hook for MenuCategory
func (mc *MenuCategory) BeforeCreate(tx *gorm.DB) error {
	if mc.ID == "" {
		mc.ID = uuid.New().String()
	}
	return nil
}

// MenuItem represents the menu_items table
type MenuItem struct {
	ID              string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	MenuID          string          `json:"menuId" gorm:"type:varchar(36);not null;index"`
	CategoryID      *string         `json:"categoryId" gorm:"type:varchar(36);index"`
	Name            string          `json:"name" gorm:"type:varchar(255);not null"`
	Description     *string         `json:"description" gorm:"type:text"`
	Price           decimal.Decimal `json:"price" gorm:"type:decimal(10,2);not null"`
	ImageURL        *string         `json:"imageUrl" gorm:"type:text"`
	IsAvailable     bool            `json:"isAvailable" gorm:"default:true;index"`
	IsActive        bool            `json:"isActive" gorm:"default:true;index"`
	DisplayOrder    int             `json:"displayOrder" gorm:"default:0;index"`
	Allergens       []string        `json:"allergens" gorm:"type:text[]"`
	Tags            []string        `json:"tags" gorm:"type:text[]"`
	PreparationTime *int            `json:"preparationTime"` // in minutes
	Calories        *int            `json:"calories"`
	CreatedAt       time.Time       `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt       time.Time       `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Menu       Menu                  `json:"menu" gorm:"foreignKey:MenuID"`
	Category   *MenuCategory         `json:"category" gorm:"foreignKey:CategoryID"`
	OrderItems []RestaurantOrderItem `json:"orderItems" gorm:"foreignKey:MenuItemID"`
}

// BeforeCreate hook for MenuItem
func (mi *MenuItem) BeforeCreate(tx *gorm.DB) error {
	if mi.ID == "" {
		mi.ID = uuid.New().String()
	}
	return nil
}

// RestaurantTable represents the restaurant_tables table
type RestaurantTable struct {
	ID           string     `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	RestaurantID string     `json:"restaurantId" gorm:"type:varchar(36);not null;index"`
	TableNumber  string     `json:"tableNumber" gorm:"type:varchar(50);not null"`
	Capacity     int        `json:"capacity" gorm:"not null"`
	Location     *string    `json:"location" gorm:"type:varchar(255)"`
	IsActive     bool       `json:"isActive" gorm:"default:true;index"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Restaurant Restaurant        `json:"restaurant" gorm:"foreignKey:RestaurantID"`
	Orders     []RestaurantOrder `json:"orders" gorm:"foreignKey:TableID"`
}

// BeforeCreate hook for RestaurantTable
func (rt *RestaurantTable) BeforeCreate(tx *gorm.DB) error {
	if rt.ID == "" {
		rt.ID = uuid.New().String()
	}
	return nil
}

// RestaurantStaffRole enum
type RestaurantStaffRole string

const (
	StaffRoleManager RestaurantStaffRole = "Manager"
	StaffRoleChef    RestaurantStaffRole = "Chef"
	StaffRoleWaiter  RestaurantStaffRole = "Waiter"
	StaffRoleCashier RestaurantStaffRole = "Cashier"
	StaffRoleHost    RestaurantStaffRole = "Host"
)

// RestaurantStaff represents the restaurant_staff table
type RestaurantStaff struct {
	ID           string              `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	RestaurantID string              `json:"restaurantId" gorm:"type:varchar(36);not null;index"`
	UserID       *string             `json:"userId" gorm:"type:varchar(36);index"`
	Name         string              `json:"name" gorm:"type:varchar(255);not null"`
	Email        *string             `json:"email" gorm:"type:varchar(255)"`
	Phone        *string             `json:"phone" gorm:"type:varchar(50)"`
	Role         RestaurantStaffRole `json:"role" gorm:"type:varchar(50);not null;index"`
	IsActive     bool                `json:"isActive" gorm:"default:true;index"`
	CreatedAt    time.Time           `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time           `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Restaurant Restaurant        `json:"restaurant" gorm:"foreignKey:RestaurantID"`
	User       *User             `json:"user" gorm:"foreignKey:UserID"`
	Orders     []RestaurantOrder `json:"orders" gorm:"foreignKey:StaffID"`
}

// BeforeCreate hook for RestaurantStaff
func (rs *RestaurantStaff) BeforeCreate(tx *gorm.DB) error {
	if rs.ID == "" {
		rs.ID = uuid.New().String()
	}
	return nil
}

// RestaurantOrderStatus enum
type RestaurantOrderStatus string

const (
	OrderStatusPending   RestaurantOrderStatus = "Pending"
	OrderStatusConfirmed RestaurantOrderStatus = "Confirmed"
	OrderStatusPreparing RestaurantOrderStatus = "Preparing"
	OrderStatusReady     RestaurantOrderStatus = "Ready"
	OrderStatusServed    RestaurantOrderStatus = "Served"
	OrderStatusCompleted RestaurantOrderStatus = "Completed"
	OrderStatusCancelled RestaurantOrderStatus = "Cancelled"
)

// RestaurantOrder represents the restaurant_orders table
type RestaurantOrder struct {
	ID            string                `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	RestaurantID  string                `json:"restaurantId" gorm:"type:varchar(36);not null;index"`
	TableID       *string               `json:"tableId" gorm:"type:varchar(36);index"`
	StaffID       *string               `json:"staffId" gorm:"type:varchar(36);index"`
	OrderNumber   string                `json:"orderNumber" gorm:"type:varchar(100);not null"`
	CustomerName  *string               `json:"customerName" gorm:"type:varchar(255)"`
	CustomerPhone *string               `json:"customerPhone" gorm:"type:varchar(50)"`
	Status        RestaurantOrderStatus `json:"status" gorm:"type:varchar(50);default:'Pending';index"`
	TotalAmount   decimal.Decimal       `json:"totalAmount" gorm:"type:decimal(10,2);not null"`
	Notes         *string               `json:"notes" gorm:"type:text"`
	CreatedAt     time.Time             `json:"createdAt" gorm:"autoCreateTime;index"`
	UpdatedAt     time.Time             `json:"updatedAt" gorm:"autoUpdateTime"`

	// Relationships
	Restaurant Restaurant             `json:"restaurant" gorm:"foreignKey:RestaurantID"`
	Table      *RestaurantTable       `json:"table" gorm:"foreignKey:TableID"`
	Staff      *RestaurantStaff       `json:"staff" gorm:"foreignKey:StaffID"`
	Items      []RestaurantOrderItem  `json:"items" gorm:"foreignKey:OrderID"`
}

// BeforeCreate hook for RestaurantOrder
func (ro *RestaurantOrder) BeforeCreate(tx *gorm.DB) error {
	if ro.ID == "" {
		ro.ID = uuid.New().String()
	}
	return nil
}

// RestaurantOrderItem represents the restaurant_order_items table
type RestaurantOrderItem struct {
	ID         string          `json:"id" gorm:"type:varchar(36);primaryKey;default:gen_random_uuid()"`
	OrderID    string          `json:"orderId" gorm:"type:varchar(36);not null;index"`
	MenuItemID string          `json:"menuItemId" gorm:"type:varchar(36);not null;index"`
	Quantity   int             `json:"quantity" gorm:"not null"`
	UnitPrice  decimal.Decimal `json:"unitPrice" gorm:"type:decimal(10,2);not null"`
	TotalPrice decimal.Decimal `json:"totalPrice" gorm:"type:decimal(10,2);not null"`
	Notes      *string         `json:"notes" gorm:"type:text"`
	CreatedAt  time.Time       `json:"createdAt" gorm:"autoCreateTime"`

	// Relationships
	Order    RestaurantOrder `json:"order" gorm:"foreignKey:OrderID"`
	MenuItem MenuItem        `json:"menuItem" gorm:"foreignKey:MenuItemID"`
}

// BeforeCreate hook for RestaurantOrderItem
func (roi *RestaurantOrderItem) BeforeCreate(tx *gorm.DB) error {
	if roi.ID == "" {
		roi.ID = uuid.New().String()
	}
	return nil
}
