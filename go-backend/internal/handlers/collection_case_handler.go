package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// CollectionCaseHandler handles collection case-related HTTP requests
type CollectionCaseHandler struct {
	collectionCaseService *services.CollectionCaseService
}

// NewCollectionCaseHandler creates a new CollectionCaseHandler
func NewCollectionCaseHandler(collectionCaseService *services.CollectionCaseService) *CollectionCaseHandler {
	return &CollectionCaseHandler{
		collectionCaseService: collectionCaseService,
	}
}

// CreateCollectionCaseRequest represents the request payload for creating a collection case
type CreateCollectionCaseRequest struct {
	CustomerID      string                       `json:"customerId" binding:"required"`
	InvoiceID       *string                      `json:"invoiceId,omitempty"`
	CaseNumber      string                       `json:"caseNumber,omitempty"`
	Status          models.CollectionCaseStatus  `json:"status,omitempty"`
	Priority        string                       `json:"priority,omitempty"`
	TotalAmount     decimal.Decimal              `json:"totalAmount" binding:"required"`
	CollectedAmount decimal.Decimal              `json:"collectedAmount,omitempty"`
	AssignedTo      *string                      `json:"assignedTo,omitempty"`
	DueDate         *string                      `json:"dueDate,omitempty"`
	LastContactDate *string                      `json:"lastContactDate,omitempty"`
	NextContactDate *string                      `json:"nextContactDate,omitempty"`
	Notes           *string                      `json:"notes,omitempty"`
}

// UpdateCollectionCaseRequest represents the request payload for updating a collection case
type UpdateCollectionCaseRequest struct {
	CustomerID      *string                      `json:"customerId,omitempty"`
	InvoiceID       *string                      `json:"invoiceId,omitempty"`
	Status          *models.CollectionCaseStatus `json:"status,omitempty"`
	Priority        *string                      `json:"priority,omitempty"`
	TotalAmount     *decimal.Decimal             `json:"totalAmount,omitempty"`
	CollectedAmount *decimal.Decimal             `json:"collectedAmount,omitempty"`
	AssignedTo      *string                      `json:"assignedTo,omitempty"`
	DueDate         *string                      `json:"dueDate,omitempty"`
	LastContactDate *string                      `json:"lastContactDate,omitempty"`
	NextContactDate *string                      `json:"nextContactDate,omitempty"`
	Notes           *string                      `json:"notes,omitempty"`
}

// GetAllCollectionCases retrieves all collection cases with pagination
func (h *CollectionCaseHandler) GetAllCollectionCases(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	cases, total, err := h.collectionCaseService.GetAllCollectionCases(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionCases": cases,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCollectionCaseByID retrieves a collection case by ID
func (h *CollectionCaseHandler) GetCollectionCaseByID(c *gin.Context) {
	id := c.Param("id")

	collectionCase, err := h.collectionCaseService.GetCollectionCaseByID(id)
	if err != nil {
		if err.Error() == "collection case not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, collectionCase)
}

// GetCollectionCasesByMerchant retrieves collection cases for a specific merchant
func (h *CollectionCaseHandler) GetCollectionCasesByMerchant(c *gin.Context) {
	merchantID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse filters
	var status, priority *string
	if statusStr := c.Query("status"); statusStr != "" {
		status = &statusStr
	}
	if priorityStr := c.Query("priority"); priorityStr != "" {
		priority = &priorityStr
	}

	cases, total, err := h.collectionCaseService.GetCollectionCasesByMerchant(merchantID, page, limit, search, status, priority)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionCases": cases,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCollectionCasesByCustomer retrieves collection cases for a specific customer
func (h *CollectionCaseHandler) GetCollectionCasesByCustomer(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	cases, total, err := h.collectionCaseService.GetCollectionCasesByCustomer(customerID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionCases": cases,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateCollectionCase creates a new collection case
func (h *CollectionCaseHandler) CreateCollectionCase(c *gin.Context) {
	merchantID := c.Param("merchantId")
	var req CreateCollectionCaseRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create collection case
	collectionCase := &models.CollectionCase{
		MerchantID:      merchantID,
		CustomerID:      req.CustomerID,
		InvoiceID:       req.InvoiceID,
		CaseNumber:      req.CaseNumber,
		Status:          req.Status,
		Priority:        req.Priority,
		TotalAmount:     req.TotalAmount,
		CollectedAmount: req.CollectedAmount,
		AssignedTo:      req.AssignedTo,
		Notes:           req.Notes,
	}

	// Parse dates if provided
	if req.DueDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}
	if req.LastContactDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}
	if req.NextContactDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}

	// Create the collection case
	if err := h.collectionCaseService.CreateCollectionCase(collectionCase); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created collection case with relationships
	createdCase, err := h.collectionCaseService.GetCollectionCaseByID(collectionCase.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created collection case: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdCase)
}

// UpdateCollectionCase updates an existing collection case
func (h *CollectionCaseHandler) UpdateCollectionCase(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCollectionCaseRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CollectionCase{}
	if req.CustomerID != nil {
		updates.CustomerID = *req.CustomerID
	}
	if req.InvoiceID != nil {
		updates.InvoiceID = req.InvoiceID
	}
	if req.Status != nil {
		updates.Status = *req.Status
	}
	if req.Priority != nil {
		updates.Priority = *req.Priority
	}
	if req.TotalAmount != nil {
		updates.TotalAmount = *req.TotalAmount
	}
	if req.CollectedAmount != nil {
		updates.CollectedAmount = *req.CollectedAmount
	}
	if req.AssignedTo != nil {
		updates.AssignedTo = req.AssignedTo
	}
	if req.Notes != nil {
		updates.Notes = req.Notes
	}

	// Parse dates if provided
	if req.DueDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}
	if req.LastContactDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}
	if req.NextContactDate != nil {
		// Parse date string to time.Time - implement date parsing logic
	}

	updatedCase, err := h.collectionCaseService.UpdateCollectionCase(id, updates)
	if err != nil {
		if err.Error() == "collection case not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedCase)
}

// DeleteCollectionCase deletes a collection case
func (h *CollectionCaseHandler) DeleteCollectionCase(c *gin.Context) {
	id := c.Param("id")

	if err := h.collectionCaseService.DeleteCollectionCase(id); err != nil {
		if err.Error() == "collection case not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Collection case deleted successfully"})
}
