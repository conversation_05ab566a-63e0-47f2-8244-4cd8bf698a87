package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// RecurringInvoiceHandler handles recurring invoice-related HTTP requests
type RecurringInvoiceHandler struct {
	recurringInvoiceService *services.RecurringInvoiceService
}

// NewRecurringInvoiceHandler creates a new RecurringInvoiceHandler
func NewRecurringInvoiceHandler(recurringInvoiceService *services.RecurringInvoiceService) *RecurringInvoiceHandler {
	return &RecurringInvoiceHandler{
		recurringInvoiceService: recurringInvoiceService,
	}
}

// CreateRecurringInvoiceRequest represents the request payload for creating a recurring invoice
type CreateRecurringInvoiceRequest struct {
	CustomerID     string                         `json:"customerId" binding:"required"`
	TemplateID     *string                        `json:"templateId,omitempty"`
	Name           string                         `json:"name" binding:"required"`
	Frequency      models.RecurringFrequency      `json:"frequency" binding:"required"`
	StartDate      string                         `json:"startDate" binding:"required"`
	EndDate        *string                        `json:"endDate,omitempty"`
	Terms          *string                        `json:"terms,omitempty"`
	Notes          *string                        `json:"notes,omitempty"`
	CustomInterval *int                           `json:"customInterval,omitempty"`
	Items          []CreateRecurringInvoiceItem   `json:"items" binding:"required,min=1"`
}

// CreateRecurringInvoiceItem represents an item in the create request
type CreateRecurringInvoiceItem struct {
	Description string          `json:"description" binding:"required"`
	Quantity    decimal.Decimal `json:"quantity" binding:"required"`
	UnitPrice   decimal.Decimal `json:"unitPrice" binding:"required"`
	TaxRateID   *string         `json:"taxRateId,omitempty"`
	SortOrder   int             `json:"sortOrder,omitempty"`
}

// UpdateRecurringInvoiceRequest represents the request payload for updating a recurring invoice
type UpdateRecurringInvoiceRequest struct {
	CustomerID     *string                        `json:"customerId,omitempty"`
	TemplateID     *string                        `json:"templateId,omitempty"`
	Name           *string                        `json:"name,omitempty"`
	Frequency      *models.RecurringFrequency     `json:"frequency,omitempty"`
	StartDate      *string                        `json:"startDate,omitempty"`
	EndDate        *string                        `json:"endDate,omitempty"`
	Terms          *string                        `json:"terms,omitempty"`
	Notes          *string                        `json:"notes,omitempty"`
	CustomInterval *int                           `json:"customInterval,omitempty"`
	Status         *models.RecurringStatus        `json:"status,omitempty"`
}

// GetAllRecurringInvoices retrieves all recurring invoices with pagination
func (h *RecurringInvoiceHandler) GetAllRecurringInvoices(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	invoices, total, err := h.recurringInvoiceService.GetAllRecurringInvoices(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"recurringInvoices": invoices,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetRecurringInvoiceByID retrieves a recurring invoice by ID
func (h *RecurringInvoiceHandler) GetRecurringInvoiceByID(c *gin.Context) {
	id := c.Param("id")

	invoice, err := h.recurringInvoiceService.GetRecurringInvoiceByID(id)
	if err != nil {
		if err.Error() == "recurring invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, invoice)
}

// GetRecurringInvoicesByMerchant retrieves recurring invoices for a specific merchant
func (h *RecurringInvoiceHandler) GetRecurringInvoicesByMerchant(c *gin.Context) {
	merchantID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")
	status := c.Query("status")
	frequency := c.Query("frequency")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse date filters
	var startDate, endDate *time.Time
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsedDate
		}
	}
	if endDateStr := c.Query("endDate"); endDateStr != "" {
		if parsedDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsedDate
		}
	}

	invoices, total, err := h.recurringInvoiceService.GetRecurringInvoicesByMerchant(
		merchantID, page, limit, search, status, frequency, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"recurringInvoices": invoices,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetRecurringInvoicesByCustomer retrieves recurring invoices for a specific customer
func (h *RecurringInvoiceHandler) GetRecurringInvoicesByCustomer(c *gin.Context) {
	customerID := c.Param("customerId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	invoices, total, err := h.recurringInvoiceService.GetRecurringInvoicesByCustomer(customerID, page, limit, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"recurringInvoices": invoices,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateRecurringInvoice creates a new recurring invoice
func (h *RecurringInvoiceHandler) CreateRecurringInvoice(c *gin.Context) {
	merchantID := c.Param("merchantId")
	var req CreateRecurringInvoiceRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse start date
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use YYYY-MM-DD"})
		return
	}

	// Parse end date if provided
	var endDate *time.Time
	if req.EndDate != nil {
		if parsedEndDate, err := time.Parse("2006-01-02", *req.EndDate); err == nil {
			endDate = &parsedEndDate
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Create recurring invoice
	recurringInvoice := &models.RecurringInvoice{
		MerchantID:     merchantID,
		CustomerID:     req.CustomerID,
		TemplateID:     req.TemplateID,
		Name:           req.Name,
		Frequency:      req.Frequency,
		StartDate:      startDate,
		EndDate:        endDate,
		Terms:          req.Terms,
		Notes:          req.Notes,
		CustomInterval: req.CustomInterval,
	}

	// Convert request items to model items
	items := make([]models.RecurringInvoiceItem, len(req.Items))
	for i, item := range req.Items {
		items[i] = models.RecurringInvoiceItem{
			Description: item.Description,
			Quantity:    item.Quantity,
			UnitPrice:   item.UnitPrice,
			TaxRateID:   item.TaxRateID,
			SortOrder:   item.SortOrder,
		}
	}

	// Create the recurring invoice
	if err := h.recurringInvoiceService.CreateRecurringInvoice(recurringInvoice, items); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created recurring invoice with relationships
	createdInvoice, err := h.recurringInvoiceService.GetRecurringInvoiceByID(recurringInvoice.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created recurring invoice: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdInvoice)
}

// UpdateRecurringInvoice updates an existing recurring invoice
func (h *RecurringInvoiceHandler) UpdateRecurringInvoice(c *gin.Context) {
	id := c.Param("id")
	var req UpdateRecurringInvoiceRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.RecurringInvoice{}
	if req.CustomerID != nil {
		updates.CustomerID = *req.CustomerID
	}
	if req.TemplateID != nil {
		updates.TemplateID = req.TemplateID
	}
	if req.Name != nil {
		updates.Name = *req.Name
	}
	if req.Frequency != nil {
		updates.Frequency = *req.Frequency
	}
	if req.Terms != nil {
		updates.Terms = req.Terms
	}
	if req.Notes != nil {
		updates.Notes = req.Notes
	}
	if req.CustomInterval != nil {
		updates.CustomInterval = req.CustomInterval
	}
	if req.Status != nil {
		updates.Status = *req.Status
	}

	// Parse dates if provided
	if req.StartDate != nil {
		if parsedDate, err := time.Parse("2006-01-02", *req.StartDate); err == nil {
			updates.StartDate = parsedDate
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use YYYY-MM-DD"})
			return
		}
	}
	if req.EndDate != nil {
		if parsedDate, err := time.Parse("2006-01-02", *req.EndDate); err == nil {
			updates.EndDate = &parsedDate
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use YYYY-MM-DD"})
			return
		}
	}

	updatedInvoice, err := h.recurringInvoiceService.UpdateRecurringInvoice(id, updates)
	if err != nil {
		if err.Error() == "recurring invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedInvoice)
}

// DeleteRecurringInvoice deletes a recurring invoice
func (h *RecurringInvoiceHandler) DeleteRecurringInvoice(c *gin.Context) {
	id := c.Param("id")

	if err := h.recurringInvoiceService.DeleteRecurringInvoice(id); err != nil {
		if err.Error() == "recurring invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Recurring invoice deleted successfully"})
}

// GenerateInvoice generates an invoice from a recurring invoice
func (h *RecurringInvoiceHandler) GenerateInvoice(c *gin.Context) {
	id := c.Param("id")

	invoice, err := h.recurringInvoiceService.GenerateInvoiceFromRecurring(id)
	if err != nil {
		if err.Error() == "recurring invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Invoice generated successfully",
		"invoice": invoice,
	})
}

// UpdateRecurringInvoiceStatus updates the status of a recurring invoice
func (h *RecurringInvoiceHandler) UpdateRecurringInvoiceStatus(c *gin.Context) {
	id := c.Param("id")
	
	var req struct {
		Status models.RecurringStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedInvoice, err := h.recurringInvoiceService.UpdateRecurringInvoiceStatus(id, req.Status)
	if err != nil {
		if err.Error() == "recurring invoice not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedInvoice)
}
