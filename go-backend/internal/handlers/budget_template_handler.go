package handlers

import (
	"net/http"
	"strconv"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// BudgetTemplateHandler handles budget template-related HTTP requests
type BudgetTemplateHandler struct {
	budgetTemplateService *services.BudgetTemplateService
}

// NewBudgetTemplateHandler creates a new BudgetTemplateHandler
func NewBudgetTemplateHandler(budgetTemplateService *services.BudgetTemplateService) *BudgetTemplateHandler {
	return &BudgetTemplateHandler{
		budgetTemplateService: budgetTemplateService,
	}
}

// CreateBudgetTemplateRequest represents the request payload for creating a budget template
type CreateBudgetTemplateRequest struct {
	Name        string                            `json:"name" binding:"required"`
	Description *string                           `json:"description,omitempty"`
	IsActive    *bool                             `json:"isActive,omitempty"`
	Items       []CreateBudgetTemplateItemRequest `json:"items,omitempty"`
}

// CreateBudgetTemplateItemRequest represents an item in the create request
type CreateBudgetTemplateItemRequest struct {
	AccountID string          `json:"accountId" binding:"required"`
	Amount    decimal.Decimal `json:"amount" binding:"required"`
	Notes     *string         `json:"notes,omitempty"`
}

// UpdateBudgetTemplateRequest represents the request payload for updating a budget template
type UpdateBudgetTemplateRequest struct {
	Name        *string                           `json:"name,omitempty"`
	Description *string                           `json:"description,omitempty"`
	IsActive    *bool                             `json:"isActive,omitempty"`
	Items       []CreateBudgetTemplateItemRequest `json:"items,omitempty"`
}

// GetAllBudgetTemplates retrieves all budget templates with pagination
func (h *BudgetTemplateHandler) GetAllBudgetTemplates(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	templates, total, err := h.budgetTemplateService.GetAllBudgetTemplates(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"budgetTemplates": templates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetBudgetTemplateByID retrieves a budget template by ID
func (h *BudgetTemplateHandler) GetBudgetTemplateByID(c *gin.Context) {
	id := c.Param("id")

	template, err := h.budgetTemplateService.GetBudgetTemplateByID(id)
	if err != nil {
		if err.Error() == "budget template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, template)
}

// GetBudgetTemplatesByMerchant retrieves budget templates for a specific merchant
func (h *BudgetTemplateHandler) GetBudgetTemplatesByMerchant(c *gin.Context) {
	merchantID := c.Param("merchantId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse active filter
	var isActive *bool
	if activeStr := c.Query("isActive"); activeStr != "" {
		if activeStr == "true" {
			active := true
			isActive = &active
		} else if activeStr == "false" {
			active := false
			isActive = &active
		}
	}

	templates, total, err := h.budgetTemplateService.GetBudgetTemplatesByMerchant(merchantID, page, limit, search, isActive)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"budgetTemplates": templates,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateBudgetTemplate creates a new budget template
func (h *BudgetTemplateHandler) CreateBudgetTemplate(c *gin.Context) {
	merchantID := c.Param("merchantId")
	var req CreateBudgetTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create budget template
	template := &models.BudgetTemplate{
		MerchantID:  merchantID,
		Name:        req.Name,
		Description: req.Description,
	}

	// Set isActive
	if req.IsActive != nil {
		template.IsActive = *req.IsActive
	} else {
		template.IsActive = true
	}

	// Convert request items to model items
	items := make([]models.BudgetTemplateItem, len(req.Items))
	for i, itemReq := range req.Items {
		items[i] = models.BudgetTemplateItem{
			AccountID: itemReq.AccountID,
			Amount:    itemReq.Amount,
			Notes:     itemReq.Notes,
		}
	}

	template.Items = items

	// Create the budget template
	if err := h.budgetTemplateService.CreateBudgetTemplate(template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created budget template with relationships
	createdTemplate, err := h.budgetTemplateService.GetBudgetTemplateByID(template.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created budget template: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdTemplate)
}

// UpdateBudgetTemplate updates an existing budget template
func (h *BudgetTemplateHandler) UpdateBudgetTemplate(c *gin.Context) {
	id := c.Param("id")
	var req UpdateBudgetTemplateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.BudgetTemplate{}
	if req.Name != nil {
		updates.Name = *req.Name
	}
	if req.Description != nil {
		updates.Description = req.Description
	}
	if req.IsActive != nil {
		updates.IsActive = *req.IsActive
	}

	// Convert request items to model items if provided
	if len(req.Items) > 0 {
		items := make([]models.BudgetTemplateItem, len(req.Items))
		for i, itemReq := range req.Items {
			items[i] = models.BudgetTemplateItem{
				AccountID: itemReq.AccountID,
				Amount:    itemReq.Amount,
				Notes:     itemReq.Notes,
			}
		}
		updates.Items = items
	}

	updatedTemplate, err := h.budgetTemplateService.UpdateBudgetTemplate(id, updates)
	if err != nil {
		if err.Error() == "budget template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedTemplate)
}

// DeleteBudgetTemplate deletes a budget template
func (h *BudgetTemplateHandler) DeleteBudgetTemplate(c *gin.Context) {
	id := c.Param("id")

	if err := h.budgetTemplateService.DeleteBudgetTemplate(id); err != nil {
		if err.Error() == "budget template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Budget template deleted successfully"})
}

// GetActiveBudgetTemplates gets active budget templates for a merchant
func (h *BudgetTemplateHandler) GetActiveBudgetTemplates(c *gin.Context) {
	merchantID := c.Param("merchantId")

	templates, err := h.budgetTemplateService.GetActiveBudgetTemplates(merchantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"budgetTemplates": templates})
}

// ApplyBudgetTemplate applies a budget template to create budget items for a specific year
func (h *BudgetTemplateHandler) ApplyBudgetTemplate(c *gin.Context) {
	templateID := c.Param("id")
	
	var req struct {
		Year int `json:"year" binding:"required,min=2000,max=2100"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	budgetItems, err := h.budgetTemplateService.ApplyBudgetTemplate(templateID, req.Year)
	if err != nil {
		if err.Error() == "budget template not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":     "Budget template applied successfully",
		"budgetItems": budgetItems,
		"year":        req.Year,
		"itemsCreated": len(budgetItems),
	})
}

// GetBudgetTemplateItems gets items for a specific template
func (h *BudgetTemplateHandler) GetBudgetTemplateItems(c *gin.Context) {
	templateID := c.Param("id")

	items, err := h.budgetTemplateService.GetBudgetTemplateItems(templateID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"items": items})
}

// CreateBudgetTemplateItem creates a new item for a template
func (h *BudgetTemplateHandler) CreateBudgetTemplateItem(c *gin.Context) {
	templateID := c.Param("id")
	var req CreateBudgetTemplateItemRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create item
	item := &models.BudgetTemplateItem{
		TemplateID: templateID,
		AccountID:  req.AccountID,
		Amount:     req.Amount,
		Notes:      req.Notes,
	}

	// Create the item
	if err := h.budgetTemplateService.CreateBudgetTemplateItem(item); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, item)
}

// UpdateBudgetTemplateItem updates an existing template item
func (h *BudgetTemplateHandler) UpdateBudgetTemplateItem(c *gin.Context) {
	itemID := c.Param("itemId")
	var req CreateBudgetTemplateItemRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.BudgetTemplateItem{
		AccountID: req.AccountID,
		Amount:    req.Amount,
		Notes:     req.Notes,
	}

	updatedItem, err := h.budgetTemplateService.UpdateBudgetTemplateItem(itemID, updates)
	if err != nil {
		if err.Error() == "budget template item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedItem)
}

// DeleteBudgetTemplateItem deletes a template item
func (h *BudgetTemplateHandler) DeleteBudgetTemplateItem(c *gin.Context) {
	itemID := c.Param("itemId")

	if err := h.budgetTemplateService.DeleteBudgetTemplateItem(itemID); err != nil {
		if err.Error() == "budget template item not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Budget template item deleted successfully"})
}
