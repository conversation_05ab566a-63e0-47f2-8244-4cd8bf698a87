package handlers

import (
	"net/http"
	"strconv"
	"time"

	"adc-account-backend/internal/models"
	"adc-account-backend/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

// CollectionActivityHandler handles collection activity-related HTTP requests
type CollectionActivityHandler struct {
	collectionActivityService *services.CollectionActivityService
}

// NewCollectionActivityHandler creates a new CollectionActivityHandler
func NewCollectionActivityHandler(collectionActivityService *services.CollectionActivityService) *CollectionActivityHandler {
	return &CollectionActivityHandler{
		collectionActivityService: collectionActivityService,
	}
}

// CreateCollectionActivityRequest represents the request payload for creating a collection activity
type CreateCollectionActivityRequest struct {
	CollectionCaseID string                         `json:"collectionCaseId" binding:"required"`
	Type             models.CollectionActivityType  `json:"type" binding:"required"`
	Date             *string                        `json:"date,omitempty"`
	Description      string                         `json:"description" binding:"required"`
	Amount           *decimal.Decimal               `json:"amount,omitempty"`
	ContactMethod    *string                        `json:"contactMethod,omitempty"`
	Outcome          *string                        `json:"outcome,omitempty"`
	NextAction       *string                        `json:"nextAction,omitempty"`
	NextActionDate   *string                        `json:"nextActionDate,omitempty"`
	CreatedBy        string                         `json:"createdBy" binding:"required"`
}

// UpdateCollectionActivityRequest represents the request payload for updating a collection activity
type UpdateCollectionActivityRequest struct {
	Type           *models.CollectionActivityType `json:"type,omitempty"`
	Date           *string                        `json:"date,omitempty"`
	Description    *string                        `json:"description,omitempty"`
	Amount         *decimal.Decimal               `json:"amount,omitempty"`
	ContactMethod  *string                        `json:"contactMethod,omitempty"`
	Outcome        *string                        `json:"outcome,omitempty"`
	NextAction     *string                        `json:"nextAction,omitempty"`
	NextActionDate *string                        `json:"nextActionDate,omitempty"`
}

// GetAllCollectionActivities retrieves all collection activities with pagination
func (h *CollectionActivityHandler) GetAllCollectionActivities(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	search := c.Query("search")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	activities, total, err := h.collectionActivityService.GetAllCollectionActivities(page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionActivities": activities,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCollectionActivityByID retrieves a collection activity by ID
func (h *CollectionActivityHandler) GetCollectionActivityByID(c *gin.Context) {
	id := c.Param("id")

	activity, err := h.collectionActivityService.GetCollectionActivityByID(id)
	if err != nil {
		if err.Error() == "collection activity not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, activity)
}

// GetCollectionActivitiesByCase retrieves collection activities for a specific case
func (h *CollectionActivityHandler) GetCollectionActivitiesByCase(c *gin.Context) {
	caseID := c.Param("caseId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse activity type filter
	var activityType *string
	if typeStr := c.Query("type"); typeStr != "" {
		activityType = &typeStr
	}

	activities, total, err := h.collectionActivityService.GetCollectionActivitiesByCase(caseID, page, limit, activityType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionActivities": activities,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// GetCollectionActivitiesByCreator retrieves collection activities created by a specific user
func (h *CollectionActivityHandler) GetCollectionActivitiesByCreator(c *gin.Context) {
	creatorID := c.Param("creatorId")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	activities, total, err := h.collectionActivityService.GetCollectionActivitiesByCreator(creatorID, page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)
	hasNextPage := page < int(totalPages)
	hasPreviousPage := page > 1

	c.JSON(http.StatusOK, gin.H{
		"collectionActivities": activities,
		"pagination": gin.H{
			"total":           total,
			"page":            page,
			"limit":           limit,
			"totalPages":      totalPages,
			"hasNextPage":     hasNextPage,
			"hasPreviousPage": hasPreviousPage,
		},
	})
}

// CreateCollectionActivity creates a new collection activity
func (h *CollectionActivityHandler) CreateCollectionActivity(c *gin.Context) {
	var req CreateCollectionActivityRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create collection activity
	activity := &models.CollectionActivity{
		CollectionCaseID: req.CollectionCaseID,
		Type:             req.Type,
		Description:      req.Description,
		Amount:           req.Amount,
		ContactMethod:    req.ContactMethod,
		Outcome:          req.Outcome,
		NextAction:       req.NextAction,
		CreatedBy:        req.CreatedBy,
	}

	// Parse dates if provided
	if req.Date != nil {
		if parsedDate, err := time.Parse(time.RFC3339, *req.Date); err == nil {
			activity.Date = parsedDate
		}
	}
	if req.NextActionDate != nil {
		if parsedDate, err := time.Parse(time.RFC3339, *req.NextActionDate); err == nil {
			activity.NextActionDate = &parsedDate
		}
	}

	// Create the collection activity
	if err := h.collectionActivityService.CreateCollectionActivity(activity); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Fetch the created collection activity with relationships
	createdActivity, err := h.collectionActivityService.GetCollectionActivityByID(activity.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch created collection activity: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdActivity)
}

// UpdateCollectionActivity updates an existing collection activity
func (h *CollectionActivityHandler) UpdateCollectionActivity(c *gin.Context) {
	id := c.Param("id")
	var req UpdateCollectionActivityRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create updates model
	updates := &models.CollectionActivity{}
	if req.Type != nil {
		updates.Type = *req.Type
	}
	if req.Description != nil {
		updates.Description = *req.Description
	}
	if req.Amount != nil {
		updates.Amount = req.Amount
	}
	if req.ContactMethod != nil {
		updates.ContactMethod = req.ContactMethod
	}
	if req.Outcome != nil {
		updates.Outcome = req.Outcome
	}
	if req.NextAction != nil {
		updates.NextAction = req.NextAction
	}

	// Parse dates if provided
	if req.Date != nil {
		if parsedDate, err := time.Parse(time.RFC3339, *req.Date); err == nil {
			updates.Date = parsedDate
		}
	}
	if req.NextActionDate != nil {
		if parsedDate, err := time.Parse(time.RFC3339, *req.NextActionDate); err == nil {
			updates.NextActionDate = &parsedDate
		}
	}

	updatedActivity, err := h.collectionActivityService.UpdateCollectionActivity(id, updates)
	if err != nil {
		if err.Error() == "collection activity not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedActivity)
}

// DeleteCollectionActivity deletes a collection activity
func (h *CollectionActivityHandler) DeleteCollectionActivity(c *gin.Context) {
	id := c.Param("id")

	if err := h.collectionActivityService.DeleteCollectionActivity(id); err != nil {
		if err.Error() == "collection activity not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Collection activity deleted successfully"})
}

// GetActivitySummary gets summary statistics for collection activities
func (h *CollectionActivityHandler) GetActivitySummary(c *gin.Context) {
	// Parse optional filters
	var caseID, creatorID *string
	if caseIDStr := c.Query("caseId"); caseIDStr != "" {
		caseID = &caseIDStr
	}
	if creatorIDStr := c.Query("creatorId"); creatorIDStr != "" {
		creatorID = &creatorIDStr
	}

	summary, err := h.collectionActivityService.GetActivitySummary(caseID, creatorID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, summary)
}
