package handlers

import (
	"net/http"
	"strconv"

	"github.com/adc-account/go-backend/internal/models"
	"github.com/adc-account/go-backend/internal/services"
	"github.com/gin-gonic/gin"
)

type RestaurantHandler struct {
	restaurantService *services.RestaurantService
}

func NewRestaurantHandler(restaurantService *services.RestaurantService) *RestaurantHandler {
	return &RestaurantHandler{
		restaurantService: restaurantService,
	}
}

// Restaurant endpoints
func (h *RestaurantHandler) CreateRestaurant(c *gin.Context) {
	var restaurant models.Restaurant
	if err := c.ShouldBindJSON(&restaurant); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get merchant ID from context (set by auth middleware)
	merchantID, exists := c.Get("merchantId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Merchant ID not found"})
		return
	}
	restaurant.MerchantID = merchantID.(string)

	if err := h.restaurantService.CreateRestaurant(&restaurant); err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, restaurant)
}

func (h *RestaurantHandler) GetRestaurant(c *gin.Context) {
	id := c.Param("id")
	restaurant, err := h.restaurantService.GetRestaurantByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Restaurant not found"})
		return
	}

	c.JSON(http.StatusOK, restaurant)
}

func (h *RestaurantHandler) GetAllRestaurants(c *gin.Context) {
	merchantID, exists := c.Get("merchantId")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Merchant ID not found"})
		return
	}

	restaurants, err := h.restaurantService.GetRestaurantsByMerchantID(merchantID.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, restaurants)
}

func (h *RestaurantHandler) UpdateRestaurant(c *gin.Context) {
	id := c.Param("id")
	var restaurant models.Restaurant
	if err := c.ShouldBindJSON(&restaurant); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	restaurant.ID = id
	if err := h.restaurantService.UpdateRestaurant(&restaurant); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, restaurant)
}

func (h *RestaurantHandler) DeleteRestaurant(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.DeleteRestaurant(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Restaurant deleted successfully"})
}

// Menu endpoints
func (h *RestaurantHandler) CreateMenu(c *gin.Context) {
	restaurantID := c.Param("restaurantId")
	var menu models.Menu
	if err := c.ShouldBindJSON(&menu); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	menu.RestaurantID = restaurantID
	if err := h.restaurantService.CreateMenu(&menu); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, menu)
}

func (h *RestaurantHandler) GetMenu(c *gin.Context) {
	id := c.Param("id")
	menu, err := h.restaurantService.GetMenuByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu not found"})
		return
	}

	c.JSON(http.StatusOK, menu)
}

func (h *RestaurantHandler) GetRestaurantMenus(c *gin.Context) {
	restaurantID := c.Param("restaurantId")
	menus, err := h.restaurantService.GetMenusByRestaurantID(restaurantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, menus)
}

func (h *RestaurantHandler) UpdateMenu(c *gin.Context) {
	id := c.Param("id")
	var menu models.Menu
	if err := c.ShouldBindJSON(&menu); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	menu.ID = id
	if err := h.restaurantService.UpdateMenu(&menu); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, menu)
}

func (h *RestaurantHandler) DeleteMenu(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.DeleteMenu(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu deleted successfully"})
}

func (h *RestaurantHandler) PublishMenu(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.PublishMenu(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu published successfully"})
}

func (h *RestaurantHandler) UnpublishMenu(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.UnpublishMenu(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu unpublished successfully"})
}

// MenuCategory endpoints
func (h *RestaurantHandler) CreateMenuCategory(c *gin.Context) {
	menuID := c.Param("menuId")
	var category models.MenuCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category.MenuID = menuID
	if err := h.restaurantService.CreateMenuCategory(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, category)
}

func (h *RestaurantHandler) GetMenuCategories(c *gin.Context) {
	menuID := c.Param("menuId")
	categories, err := h.restaurantService.GetMenuCategoriesByMenuID(menuID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, categories)
}

func (h *RestaurantHandler) UpdateMenuCategory(c *gin.Context) {
	id := c.Param("id")
	var category models.MenuCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category.ID = id
	if err := h.restaurantService.UpdateMenuCategory(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, category)
}

func (h *RestaurantHandler) DeleteMenuCategory(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.DeleteMenuCategory(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu category deleted successfully"})
}

// MenuItem endpoints
func (h *RestaurantHandler) CreateMenuItem(c *gin.Context) {
	menuID := c.Param("menuId")
	var item models.MenuItem
	if err := c.ShouldBindJSON(&item); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	item.MenuID = menuID
	if err := h.restaurantService.CreateMenuItem(&item); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, item)
}

func (h *RestaurantHandler) GetMenuItem(c *gin.Context) {
	id := c.Param("id")
	item, err := h.restaurantService.GetMenuItemByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, item)
}

func (h *RestaurantHandler) GetMenuItems(c *gin.Context) {
	menuID := c.Param("menuId")
	categoryID := c.Query("categoryId")

	var items []models.MenuItem
	var err error

	if categoryID != "" {
		items, err = h.restaurantService.GetMenuItemsByCategoryID(categoryID)
	} else {
		items, err = h.restaurantService.GetMenuItemsByMenuID(menuID)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, items)
}

func (h *RestaurantHandler) UpdateMenuItem(c *gin.Context) {
	id := c.Param("id")
	var item models.MenuItem
	if err := c.ShouldBindJSON(&item); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	item.ID = id
	if err := h.restaurantService.UpdateMenuItem(&item); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, item)
}

func (h *RestaurantHandler) DeleteMenuItem(c *gin.Context) {
	id := c.Param("id")
	if err := h.restaurantService.DeleteMenuItem(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu item deleted successfully"})
}

func (h *RestaurantHandler) SetMenuItemAvailability(c *gin.Context) {
	id := c.Param("id")
	availableStr := c.Query("available")
	
	available, err := strconv.ParseBool(availableStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid availability value"})
		return
	}

	if err := h.restaurantService.SetMenuItemAvailability(id, available); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu item availability updated successfully"})
}

// Search endpoint
func (h *RestaurantHandler) SearchMenuItems(c *gin.Context) {
	restaurantID := c.Param("restaurantId")
	query := c.Query("q")

	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	items, err := h.restaurantService.SearchMenuItems(restaurantID, query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, items)
}
