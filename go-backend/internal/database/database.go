package database

import (
	"fmt"
	"log"

	"adc-account-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Test the connection
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")

	// Auto-migrate models
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to auto-migrate: %w", err)
	}

	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	log.Println("Running auto-migration...")

	// Define the order of migration to handle foreign key dependencies
	modelsToMigrate := []interface{}{
		// Core models first
		&models.User{},
		&models.Organization{},
		&models.Branch{},
		&models.Merchant{},

		// Permission models
		&models.UserOrganizationPermission{},
		&models.UserBranchPermission{},
		&models.UserMerchantPermission{},

		// Account and financial models
		&models.ChartOfAccount{},
		&models.TaxRate{},
		&models.BankAccount{},
		&models.BankTransaction{},
		&models.BankReconciliation{},
		&models.BankTransactionMatch{},

		// Customer and vendor models
		&models.Customer{},
		&models.Vendor{},

		// Invoice related models
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.InvoiceTemplate{},
		&models.InvoiceTemplateItem{},
		&models.RecurringInvoice{},
		&models.RecurringInvoiceItem{},

		// Bill related models
		&models.Bill{},
		&models.BillItem{},
		&models.BillPayment{},

		// Credit and payment models
		&models.CreditNote{},
		&models.CreditNoteItem{},
		&models.CreditApplication{},
		&models.CustomerCredit{},
		&models.PaymentReminder{},
		&models.CustomerStatement{},
		&models.StatementItem{},

		// Asset models
		&models.Asset{},

		// Inventory models
		&models.InventoryItem{},
		&models.InventoryTransaction{},

		// Employee and payroll models
		&models.Employee{},
		&models.PayrollRun{},
		&models.PayrollDetail{},

		// Journal entry models
		&models.JournalEntry{},
		&models.JournalEntryLine{},

		// Expense models
		&models.Expense{},

		// Budget models
		&models.BudgetItem{},
		&models.BudgetTemplate{},
		&models.BudgetTemplateItem{},

		// Cash flow models
		&models.CashFlowItem{},
		&models.RecurringCashFlowItem{},
		&models.CashFlowCategory{},

		// Collection models
		&models.CollectionCase{},
		&models.CollectionActivity{},
		&models.CollectionTemplate{},
		&models.CollectionTemplateStep{},

		// Email models
		&models.EmailTemplate{},
		&models.EmailLog{},

		// Order models
		&models.SalesOrder{},

		// Restaurant models
		&models.Restaurant{},
		&models.Menu{},
		&models.MenuCategory{},
		&models.MenuItem{},
		&models.RestaurantTable{},
		&models.RestaurantStaff{},
		&models.RestaurantOrder{},
		&models.RestaurantOrderItem{},

		// Tax models
		&models.TaxReport{},

		// Subscription models
		&models.Subscription{},
		&models.UsageRecord{},

		// API and audit models
		&models.ApiKey{},
		&models.AuditLog{},

		// Auth models
		&models.Account{},
		&models.Session{},
		&models.VerificationToken{},
		&models.RefreshToken{},
		&models.BlacklistedToken{},
		&models.LoginAttempt{},
		&models.PasswordReset{},
		&models.UserPreferences{},
	}

	for _, model := range modelsToMigrate {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	log.Println("Auto-migration completed successfully")
	return nil
}
