# ADC Account - Testing Guide

This document provides comprehensive information about testing in the ADC Account project, including page tests, component tests, and testing best practices.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Test Structure](#test-structure)
3. [Running Tests](#running-tests)
4. [Page Tests](#page-tests)
5. [Test Coverage](#test-coverage)
6. [Writing Tests](#writing-tests)
7. [Mocking Strategy](#mocking-strategy)
8. [CI/CD Integration](#cicd-integration)

## 🎯 Overview

The ADC Account project uses **Vitest** as the primary testing framework with **React Testing Library** for component testing. Our testing strategy focuses on:

- **Page-level testing**: Testing all pages in `src/app/[locale]/`
- **Component testing**: Testing reusable components
- **Integration testing**: Testing API integrations
- **Accessibility testing**: Ensuring WCAG compliance

## 🏗️ Test Structure

```
src/
├── app/[locale]/
│   ├── dashboard/
│   │   ├── __tests__/
│   │   │   └── page.test.tsx
│   │   └── page.tsx
│   ├── login/
│   │   ├── __tests__/
│   │   │   └── page.test.tsx
│   │   └── page.tsx
│   └── ...
├── components/
│   ├── common/
│   │   ├── __tests__/
│   │   │   ├── StateManager.test.tsx
│   │   │   └── PaginatedTable.test.tsx
│   │   └── ...
│   └── ...
├── test/
│   └── setup.ts
└── ...
```

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run page tests specifically
npm run test:pages

# Run page tests in watch mode
npm run test:pages:watch

# Run page tests with coverage
npm run test:pages:coverage

# Generate test report
npm run test:pages:report
```

### Advanced Commands

```bash
# Run specific test file
npm run test:pages -- --file src/app/[locale]/dashboard/__tests__/page.test.tsx

# Run tests with coverage threshold
npm run test:pages:coverage -- --coverage.threshold.lines=80

# Generate and view test report
npm run test:pages:report && open test-results.json
```

## 📄 Page Tests

We have comprehensive test coverage for all pages in the application:

### ✅ Completed Page Tests

| Page | Test File | Status | Coverage |
|------|-----------|--------|----------|
| Home | `src/app/[locale]/__tests__/page.test.tsx` | ✅ | 100% |
| Login | `src/app/[locale]/login/__tests__/page.test.tsx` | ✅ | 95% |
| Register | `src/app/[locale]/register/__tests__/page.test.tsx` | ✅ | 95% |
| Dashboard | `src/app/[locale]/dashboard/__tests__/page.test.tsx` | ✅ | 90% |
| Settings | `src/app/[locale]/settings/__tests__/page.test.tsx` | ✅ | 85% |
| Invoices | `src/app/[locale]/invoices/__tests__/page.test.tsx` | ✅ | 90% |
| Customers | `src/app/[locale]/customers/__tests__/page.test.tsx` | ✅ | 90% |
| Vendors | `src/app/[locale]/vendors/__tests__/page.test.tsx` | ✅ | 90% |
| Help | `src/app/[locale]/help/__tests__/page.test.tsx` | ✅ | 85% |
| Not Done | `src/app/[locale]/not-done/__tests__/page.test.tsx` | ✅ | 100% |
| Custom Not Done | `src/app/[locale]/custom-not-done/__tests__/page.test.tsx` | ✅ | 100% |

### 🔄 Test Categories

#### 1. Authentication Pages
- **Login Page**: Form validation, authentication flow, error handling
- **Register Page**: Registration form, validation, success/error states

#### 2. Financial Management Pages
- **Dashboard**: Data loading, charts, financial summaries
- **Invoices**: CRUD operations, filtering, pagination
- **Customers**: Customer management, search, export
- **Vendors**: Vendor management, categories, payments
- **Bills**: Bill processing, approvals, payments
- **Assets**: Asset tracking, depreciation, disposal

#### 3. Settings & Configuration
- **Settings**: User preferences, theme, language
- **Organizations**: Multi-tenant management
- **Users**: User management, permissions

#### 4. Utility Pages
- **Help**: Documentation, search, support
- **Not Done**: Placeholder pages, navigation
- **Error**: Error handling, recovery

## 📊 Test Coverage

### Current Coverage Metrics

```
Overall Coverage: 85%
├── Statements: 87%
├── Branches: 83%
├── Functions: 86%
└── Lines: 85%

Page Coverage: 90%
├── Authentication: 95%
├── Financial: 88%
├── Settings: 85%
└── Utility: 92%
```

### Coverage Goals

- **Overall**: 90%+ coverage
- **Critical paths**: 95%+ coverage
- **New features**: 100% coverage requirement

## ✍️ Writing Tests

### Test Template

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import userEvent from '@testing-library/user-event'
import YourPage from '../page'

// Mock dependencies
vi.mock('@/redux/services/yourApi', () => ({
  useGetDataQuery: vi.fn(),
  useCreateMutation: vi.fn(),
}))

describe('YourPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders page content', () => {
    render(<YourPage />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    render(<YourPage />)
    
    const button = screen.getByRole('button', { name: /click me/i })
    await user.click(button)
    
    expect(/* assertion */).toBeTruthy()
  })
})
```

### Best Practices

1. **Test user behavior, not implementation**
2. **Use semantic queries** (`getByRole`, `getByLabelText`)
3. **Mock external dependencies**
4. **Test error states and edge cases**
5. **Ensure accessibility compliance**

## 🎭 Mocking Strategy

### Global Mocks (in `src/test/setup.ts`)

```typescript
// Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({ push: vi.fn() }),
  useSearchParams: () => new URLSearchParams(),
}))

// Internationalization
vi.mock('next-intl', () => ({
  useTranslations: () => (key: string) => key,
  useLocale: () => 'en',
}))

// Redux API hooks
vi.mock('@/redux/services/dashboardApi', () => ({
  useGetFinancialSummaryQuery: () => ({ data: null, isLoading: false }),
}))
```

### Component-Specific Mocks

```typescript
// Mock complex components
vi.mock('@/components/common/StateManager', () => ({
  default: ({ children, isLoading, error }: any) => {
    if (isLoading) return <div data-testid="loading">Loading...</div>
    if (error) return <div data-testid="error">Error</div>
    return <div data-testid="state-manager">{children}</div>
  },
}))
```

## 🔄 CI/CD Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:pages:coverage
      - run: npm run test:pages:report
```

### Quality Gates

- **Minimum coverage**: 80%
- **No failing tests**: Required for merge
- **Performance**: Tests must complete within 5 minutes

## 🛠️ Troubleshooting

### Common Issues

1. **Mock not working**: Check import paths and mock placement
2. **Async tests failing**: Use `waitFor` for async operations
3. **Component not rendering**: Verify all required props are provided

### Debug Commands

```bash
# Run tests with verbose output
npm run test:pages -- --reporter=verbose

# Run single test with debugging
npm run test:pages -- --file path/to/test.tsx --reporter=verbose

# Check test coverage for specific file
npx vitest run --coverage path/to/test.tsx
```

## 📚 Resources

- [Vitest Documentation](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing](https://testing-library.com/docs/guide-accessibility/)

---

**Last Updated**: January 2025  
**Maintained by**: ADC Account Development Team
