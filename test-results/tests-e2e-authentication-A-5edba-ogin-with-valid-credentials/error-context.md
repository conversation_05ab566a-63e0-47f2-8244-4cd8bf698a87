# Test info

- Name: Authentication Flow >> should successfully login with valid credentials
- Location: /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/authentication.test.js:125:3

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/login**" until "load"
============================================================
    at /Users/<USER>/Desktop/adc/adc-account-web/tests/e2e/authentication.test.js:140:16
```

# Page snapshot

```yaml
- banner:
  - link "ADC Account":
    - /url: /
  - navigation:
    - link "Dashboard":
      - /url: /dashboard
    - link "Reports":
      - /url: /reports
  - button "Toggle theme"
  - button "🇺🇸 English"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Create Account Enter your details below to create an account Name (Optional)
  - textbox "Name (Optional)": Login Test User
  - text: Email
  - textbox "Email": <EMAIL>
  - text: Password
  - textbox "Password": TestPassword123!
  - paragraph: Password must be at least 8 characters long and include a number and a special character
  - text: Confirm Password
  - textbox "Confirm Password": TestPassword123!
  - button "Sign Up"
  - text: Already have an account?
  - link "Sign In":
    - /url: /en/en/login
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
   40 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   41 |     
   42 |     // Check Google OAuth button
   43 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
   44 |     
   45 |     // Check sign up link
   46 |     await expect(page.locator('a:has-text("Sign up")')).toBeVisible();
   47 |   });
   48 |
   49 |   test('should show validation error for invalid credentials', async ({ page }) => {
   50 |     await page.goto('http://localhost:3000/en/login');
   51 |     
   52 |     // Fill invalid credentials
   53 |     await page.fill('input[type="email"]', '<EMAIL>');
   54 |     await page.fill('input[type="password"]', 'wrongpassword');
   55 |     
   56 |     // Submit form
   57 |     await page.click('button[type="submit"]');
   58 |     
   59 |     // Wait for error message
   60 |     await expect(page.locator('[role="alert"], .error, .text-red-500, .text-destructive')).toBeVisible();
   61 |   });
   62 |
   63 |   test('should navigate to registration page', async ({ page }) => {
   64 |     await page.goto('http://localhost:3000/en/login');
   65 |     
   66 |     // Click sign up link
   67 |     await page.click('a:has-text("Sign up")');
   68 |     
   69 |     // Verify we're on registration page
   70 |     await page.waitForURL('**/register**');
   71 |     expect(page.url()).toContain('/register');
   72 |   });
   73 |
   74 |   test('should display registration form with all required fields', async ({ page }) => {
   75 |     await page.goto('http://localhost:3000/en/register');
   76 |     
   77 |     // Check form elements
   78 |     await expect(page.locator('input[name="name"]')).toBeVisible();
   79 |     await expect(page.locator('input[name="email"]')).toBeVisible();
   80 |     await expect(page.locator('input[name="password"]')).toBeVisible();
   81 |     await expect(page.locator('input[name="confirmPassword"]')).toBeVisible();
   82 |     await expect(page.locator('button[type="submit"]')).toBeVisible();
   83 |     
   84 |     // Check sign in link
   85 |     await expect(page.locator('a:has-text("Sign In")')).toBeVisible();
   86 |   });
   87 |
   88 |   test('should validate password confirmation', async ({ page }) => {
   89 |     await page.goto('http://localhost:3000/en/register');
   90 |     
   91 |     // Fill form with mismatched passwords
   92 |     await page.fill('input[name="name"]', 'Test User');
   93 |     await page.fill('input[name="email"]', '<EMAIL>');
   94 |     await page.fill('input[name="password"]', 'password123');
   95 |     await page.fill('input[name="confirmPassword"]', 'differentpassword');
   96 |     
   97 |     // Submit form
   98 |     await page.click('button[type="submit"]');
   99 |     
  100 |     // Should show password mismatch error
  101 |     // Note: This depends on client-side validation implementation
  102 |   });
  103 |
  104 |   test('should successfully register a new user', async ({ page }) => {
  105 |     await page.goto('http://localhost:3000/en/register');
  106 |     
  107 |     const timestamp = Date.now();
  108 |     const email = `test${timestamp}@example.com`;
  109 |     
  110 |     // Fill registration form
  111 |     await page.fill('input[name="name"]', 'Test User');
  112 |     await page.fill('input[name="email"]', email);
  113 |     await page.fill('input[name="password"]', 'TestPassword123!');
  114 |     await page.fill('input[name="confirmPassword"]', 'TestPassword123!');
  115 |     
  116 |     // Submit form
  117 |     await page.click('button[type="submit"]');
  118 |     
  119 |     // Should redirect to login page with success message
  120 |     await page.waitForURL('**/login**');
  121 |     expect(page.url()).toContain('/login');
  122 |     expect(page.url()).toContain('message=Account created successfully');
  123 |   });
  124 |
  125 |   test('should successfully login with valid credentials', async ({ page }) => {
  126 |     // First register a user
  127 |     await page.goto('http://localhost:3000/en/register');
  128 |     
  129 |     const timestamp = Date.now();
  130 |     const email = `logintest${timestamp}@example.com`;
  131 |     const password = 'TestPassword123!';
  132 |     
  133 |     await page.fill('input[name="name"]', 'Login Test User');
  134 |     await page.fill('input[name="email"]', email);
  135 |     await page.fill('input[name="password"]', password);
  136 |     await page.fill('input[name="confirmPassword"]', password);
  137 |     await page.click('button[type="submit"]');
  138 |     
  139 |     // Wait for redirect to login
> 140 |     await page.waitForURL('**/login**');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  141 |     
  142 |     // Now login with the created user
  143 |     await page.fill('input[type="email"]', email);
  144 |     await page.fill('input[type="password"]', password);
  145 |     await page.click('button[type="submit"]');
  146 |     
  147 |     // Should redirect to dashboard
  148 |     await page.waitForURL('**/dashboard**');
  149 |     expect(page.url()).toContain('/dashboard');
  150 |   });
  151 |
  152 |   test('should initiate Google OAuth flow', async ({ page }) => {
  153 |     await page.goto('http://localhost:3000/en/login');
  154 |     
  155 |     // Click Google OAuth button
  156 |     const googleButton = page.locator('button:has-text("Continue with Google")');
  157 |     await expect(googleButton).toBeVisible();
  158 |     
  159 |     // Click and wait for navigation to Google
  160 |     await googleButton.click();
  161 |     
  162 |     // Should redirect to Google OAuth
  163 |     await page.waitForURL('**/accounts.google.com/**');
  164 |     expect(page.url()).toContain('accounts.google.com');
  165 |   });
  166 |
  167 |   test('should protect authenticated routes', async ({ page }) => {
  168 |     // Try to access dashboard without authentication
  169 |     await page.goto('http://localhost:3000/en/dashboard');
  170 |     
  171 |     // Should redirect to login
  172 |     await page.waitForURL('**/login**');
  173 |     expect(page.url()).toContain('/login');
  174 |     expect(page.url()).toContain('callbackUrl=%2Fen%2Fdashboard');
  175 |   });
  176 |
  177 |   test('should handle language switching', async ({ page }) => {
  178 |     await page.goto('http://localhost:3000/en/login');
  179 |     
  180 |     // Check for language switcher
  181 |     const languageButton = page.locator('button:has-text("English")');
  182 |     if (await languageButton.isVisible()) {
  183 |       await languageButton.click();
  184 |       
  185 |       // Check if Thai option is available
  186 |       const thaiOption = page.locator('text=ไทย');
  187 |       if (await thaiOption.isVisible()) {
  188 |         await thaiOption.click();
  189 |         
  190 |         // Should redirect to Thai version
  191 |         await page.waitForURL('**/th/login**');
  192 |         expect(page.url()).toContain('/th/login');
  193 |       }
  194 |     }
  195 |   });
  196 | });
  197 |
  198 | test.describe('Session Management', () => {
  199 |   test('should maintain session across page reloads', async ({ page }) => {
  200 |     // Login first
  201 |     await page.goto('http://localhost:3000/en/login');
  202 |     
  203 |     // Use existing test user credentials
  204 |     await page.fill('input[type="email"]', '<EMAIL>');
  205 |     await page.fill('input[type="password"]', 'TestPassword123!');
  206 |     await page.click('button[type="submit"]');
  207 |     
  208 |     // Wait for dashboard
  209 |     await page.waitForURL('**/dashboard**');
  210 |     
  211 |     // Reload page
  212 |     await page.reload();
  213 |     
  214 |     // Should still be on dashboard (session maintained)
  215 |     expect(page.url()).toContain('/dashboard');
  216 |   });
  217 |
  218 |   test('should redirect authenticated users away from auth pages', async ({ page }) => {
  219 |     // Login first
  220 |     await page.goto('http://localhost:3000/en/login');
  221 |     await page.fill('input[type="email"]', '<EMAIL>');
  222 |     await page.fill('input[type="password"]', 'TestPassword123!');
  223 |     await page.click('button[type="submit"]');
  224 |     await page.waitForURL('**/dashboard**');
  225 |     
  226 |     // Try to access login page while authenticated
  227 |     await page.goto('http://localhost:3000/en/login');
  228 |     
  229 |     // Should redirect to dashboard
  230 |     await page.waitForURL('**/dashboard**');
  231 |     expect(page.url()).toContain('/dashboard');
  232 |   });
  233 | });
  234 |
```