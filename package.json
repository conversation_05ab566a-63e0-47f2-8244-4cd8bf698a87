{"name": "out", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:stripe": "./scripts/dev-with-stripe.sh", "build": "next build", "start": "NODE_OPTIONS='--max-old-space-size=2048' next start --port ${PORT:-3000}", "lint": "next lint", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:pages": "ts-node scripts/run-page-tests.ts", "test:pages:watch": "ts-node scripts/run-page-tests.ts -- --watch", "test:pages:coverage": "ts-node scripts/run-page-tests.ts -- --coverage", "test:pages:report": "ts-node scripts/run-page-tests.ts -- --report", "generate:tests": "ts-node scripts/generate-page-tests.ts", "stripe:webhook": "./scripts/stripe-webhook.sh", "setup:dev-user": "cross-env PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1 ts-node -P tsconfig.seed.json scripts/create-dev-user.ts", "migrate": "prisma migrate deploy", "migrate:audit": "chmod +x ./scripts/migrate-audit-log.sh && ./scripts/migrate-audit-log.sh", "deploy": "./scripts/deploy-to-cloud-run.sh", "build:cloud": "./scripts/build-with-cloud-build.sh"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^5.0.1", "@playwright/test": "^1.52.0", "@prisma/client": "^6.5.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.7.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.507.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "pg": "^8.14.1", "prisma": "^6.5.0", "puppeteer": "^24.8.2", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-redux": "^9.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "stripe": "^18.1.0", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.4", "jsdom": "^26.1.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5", "vitest": "^3.1.1", "vitest-mock-extended": "^3.1.0", "zod": "^3.24.3"}, "prisma": {"seed": "cross-env PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1 ts-node -P tsconfig.seed.json prisma/seed.ts"}}