generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                     String                      @id @default(cuid())
  name                   String?
  email                  String                      @unique
  passwordHash           String?
  createdAt              DateTime                    @default(now())
  updatedAt              DateTime                    @updatedAt
  emailVerified          DateTime?
  image                  String?
  isActive               Boolean                     @default(true)
  role                   String                      @default("staff")
  personalMerchantId     String?                     @unique // Auto-created merchant for this user
  defaultOrganizationId  String?                     // User's default organization
  accounts               Account[]
  custodianAssets        Asset[]                     @relation("AssetCustodian")
  AuditLog               AuditLog[]
  journalEntries         JournalEntry[]
  sessions               Session[]
  merchantPermissions    UserMerchantPermission[]
  organizationPermissions UserOrganizationPermission[]
  branchPermissions      UserBranchPermission[]
  preferences            UserPreferences?
  merchants              Merchant[]                  @relation("MerchantToUser")
  personalMerchant       Merchant?                   @relation("UserPersonalMerchant", fields: [personalMerchantId], references: [id])
  defaultOrganization    Organization?               @relation("UserDefaultOrganization", fields: [defaultOrganizationId], references: [id])
  restaurantStaff        RestaurantStaff[]
}

model Organization {
  id                  String                      @id @default(cuid())
  name                String
  description         String?
  address             String?
  phone               String?
  email               String?
  website             String?
  taxId               String?
  logoUrl             String?
  isActive            Boolean                     @default(true)
  createdAt           DateTime                    @default(now())
  updatedAt           DateTime                    @updatedAt
  currency            String                      @default("USD")
  fiscalYearStart     String?
  legalName           String?
  branches            Branch[]
  userPermissions     UserOrganizationPermission[]
  defaultUsers        User[]                      @relation("UserDefaultOrganization")

  @@index([isActive])
  @@index([name])
}

model Branch {
  id             String                 @id @default(cuid())
  organizationId String
  name           String
  description    String?
  address        String?
  phone          String?
  email          String?
  isActive       Boolean                @default(true)
  createdAt      DateTime               @default(now())
  updatedAt      DateTime               @updatedAt
  managerName    String?
  branchCode     String?
  organization   Organization           @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userPermissions UserBranchPermission[]

  @@unique([organizationId, branchCode])
  @@index([organizationId])
  @@index([isActive])
}

model UserOrganizationPermission {
  id                String          @id @default(cuid())
  userId            String
  organizationId    String
  permissionLevel   PermissionLevel @default(Staff)
  customPermissions Json?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization      Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@index([userId])
  @@index([organizationId])
}

model UserBranchPermission {
  id                String          @id @default(cuid())
  userId            String
  branchId          String
  permissionLevel   PermissionLevel @default(Staff)
  customPermissions Json?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  branch            Branch          @relation(fields: [branchId], references: [id], onDelete: Cascade)

  @@unique([userId, branchId])
  @@index([userId])
  @@index([branchId])
}

model Merchant {
  id                     String                   @id @default(cuid())
  name                   String
  address                String?
  phone                  String?
  primaryContactEmail    String?
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  currency               String                   @default("USD")
  fiscalYearStart        String?
  legalName              String?
  logoUrl                String?
  taxId                  String?
  website                String?
  stripeCustomerId       String?
  apiKeys                ApiKey[]
  assets                 Asset[]
  AuditLog               AuditLog[]
  bankAccounts           BankAccount[]
  bankReconciliations    BankReconciliation[]
  bills                  Bill[]
  budgetItems            BudgetItem[]
  budgetTemplates        BudgetTemplate[]
  cashFlowCategories     CashFlowCategory[]
  cashFlowItems          CashFlowItem[]
  chartOfAccounts        ChartOfAccount[]
  collectionCases        CollectionCase[]
  collectionTemplates    CollectionTemplate[]
  creditNotes            CreditNote[]
  customers              Customer[]
  customerStatements     CustomerStatement[]
  emailLogs              EmailLog[]
  emailTemplates         EmailTemplate[]
  employees              Employee[]
  expenses               Expense[]
  inventoryItems         InventoryItem[]
  inventoryTransactions  InventoryTransaction[]
  invoices               Invoice[]
  invoiceTemplates       InvoiceTemplate[]
  journalEntries         JournalEntry[]
  paymentReminders       PaymentReminder[]
  payrollDetails         PayrollDetail[]
  payrollRuns            PayrollRun[]
  recurringCashFlowItems RecurringCashFlowItem[]
  recurringInvoices      RecurringInvoice[]
  salesOrders            SalesOrder[]
  subscription           Subscription?
  taxRates               TaxRate[]
  taxReports             TaxReport[]
  userPermissions        UserMerchantPermission[]
  vendors                Vendor[]
  users                  User[]                   @relation("MerchantToUser")
  personalUser           User?                    @relation("UserPersonalMerchant")
  restaurants            Restaurant[]
}

model ChartOfAccount {
  id                           String             @id @default(cuid())
  merchantId                   String
  accountCode                  String?
  accountName                  String
  accountType                  AccountType
  description                  String?
  isActive                     Boolean            @default(true)
  createdAt                    DateTime           @default(now())
  updatedAt                    DateTime           @updatedAt
  accountGroup                 String?
  accountSubtype               String?
  cashFlowCategory             String?
  customFields                 Json?
  imageUrl                     String?
  normalBalance                String?
  notes                        String?
  parentAccountId              String?
  reconcilable                 Boolean?           @default(false)
  tags                         String[]           @default([])
  taxRelevant                  Boolean?           @default(false)
  assetsAsAccumDeprAccount     Asset[]            @relation("AccumDeprAccount")
  assetsAsAssetAccount         Asset[]            @relation("AssetAccount")
  assetsAsDeprExpenseAccount   Asset[]            @relation("DeprExpenseAccount")
  bankAccounts                 BankAccount?
  billsAsExpenseAccount        Bill[]             @relation("BillExpenseAccount")
  cashFlowItems                CashFlowItem[]     @relation("CashFlowAccount")
  merchant                     Merchant           @relation(fields: [merchantId], references: [id])
  parentAccount                ChartOfAccount?    @relation("AccountHierarchy", fields: [parentAccountId], references: [id])
  childAccounts                ChartOfAccount[]   @relation("AccountHierarchy")
  invoicesAsRevenueAccount     Invoice[]          @relation("InvoiceRevenueAccount")
  invoiceItemsAsRevenueAccount InvoiceItem[]      @relation("InvoiceItemRevenueAccount")
  journalEntryLines            JournalEntryLine[]

  @@unique([merchantId, accountCode])
  @@index([merchantId])
  @@index([accountType])
  @@index([parentAccountId])
  @@index([accountSubtype])
  @@index([accountGroup])
}

model JournalEntry {
  id                   String                 @id @default(cuid())
  merchantId           String
  entryDate            DateTime
  description          String
  sourceType           JournalEntrySourceType
  sourceId             String?
  createdAt            DateTime               @default(now())
  createdById          String?
  assetForDisposal     Asset?                 @relation("AssetDispJE")
  bill                 Bill?                  @relation("BillJE")
  billPayment          BillPayment?           @relation("BillPaymentJE")
  invoice              Invoice?               @relation("InvoiceJE")
  invoicePayment       InvoicePayment?        @relation("InvoicePaymentJE")
  createdByUser        User?                  @relation(fields: [createdById], references: [id])
  merchant             Merchant               @relation(fields: [merchantId], references: [id])
  assetForDepreciation Asset?                 @relation("AssetDeprJE", fields: [sourceId], references: [id])
  lines                JournalEntryLine[]
  payrollRun           PayrollRun?            @relation("PayrollRunJE")
  salesOrder           SalesOrder?            @relation("SalesOrderJE")

  @@index([merchantId])
  @@index([sourceType, sourceId])
}

model JournalEntryLine {
  id             String               @id @default(cuid())
  journalEntryId String
  accountId      String
  type           JournalEntryLineType
  amount         Decimal              @db.Decimal(12, 2)
  account        ChartOfAccount       @relation(fields: [accountId], references: [id])
  journalEntry   JournalEntry         @relation(fields: [journalEntryId], references: [id])

  @@index([journalEntryId])
  @@index([accountId])
}

model Vendor {
  id             String    @id @default(cuid())
  merchantId     String
  name           String
  contactPerson  String?
  email          String?
  phone          String?
  address        String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  isActive       Boolean   @default(true)
  accountNumber  String?
  category       String?
  city           String?
  country        String?
  creditLimit    Decimal?  @db.Decimal(12, 2)
  currentBalance Decimal?  @db.Decimal(12, 2)
  imageUrl       String?
  notes          String?
  paymentTerms   String?
  rating         Int?
  state          String?
  tags           String[]  @default([])
  taxId          String?
  website        String?
  zip            String?
  assets         Asset[]
  bills          Bill[]
  expenses       Expense[]
  merchant       Merchant  @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([isActive])
  @@index([category])
}

model Bill {
  id                       String          @id @default(cuid())
  merchantId               String
  vendorId                 String
  billNumber               String?
  billDate                 DateTime
  dueDate                  DateTime?
  subtotal                 Decimal         @db.Decimal(12, 2)
  taxAmount                Decimal         @db.Decimal(12, 2)
  totalAmount              Decimal         @db.Decimal(12, 2)
  status                   BillStatus      @default(Draft)
  expenseAccountId         String?
  associatedJournalEntryId String?         @unique
  createdAt                DateTime        @default(now())
  updatedAt                DateTime        @updatedAt
  amountDue                Decimal         @db.Decimal(12, 2)
  notes                    String?
  terms                    String?
  documentUrls             String[]        @default([])
  imageUrl                 String?
  journalEntry             JournalEntry?   @relation("BillJE", fields: [associatedJournalEntryId], references: [id])
  expenseAccount           ChartOfAccount? @relation("BillExpenseAccount", fields: [expenseAccountId], references: [id])
  merchant                 Merchant        @relation(fields: [merchantId], references: [id])
  vendor                   Vendor          @relation(fields: [vendorId], references: [id])
  items                    BillItem[]
  payments                 BillPayment[]

  @@index([merchantId])
  @@index([vendorId])
  @@index([status])
}

model BillItem {
  id          String   @id @default(cuid())
  billId      String
  description String
  quantity    Decimal  @db.Decimal(10, 2)
  unitPrice   Decimal  @db.Decimal(12, 2)
  taxRateId   String?
  taxRate     Decimal? @db.Decimal(5, 4)
  taxAmount   Decimal  @db.Decimal(12, 2)
  lineTotal   Decimal  @db.Decimal(12, 2)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  bill        Bill     @relation(fields: [billId], references: [id], onDelete: Cascade)

  @@index([billId])
}

model BillPayment {
  id                       String        @id @default(cuid())
  billId                   String
  paymentDate              DateTime
  amountPaid               Decimal       @db.Decimal(12, 2)
  paymentMethod            String?
  referenceNumber          String?
  bankAccountId            String?
  associatedJournalEntryId String?       @unique
  createdAt                DateTime      @default(now())
  journalEntry             JournalEntry? @relation("BillPaymentJE", fields: [associatedJournalEntryId], references: [id])
  bill                     Bill          @relation(fields: [billId], references: [id])

  @@index([billId])
}

model SalesOrder {
  id                       String        @id @default(cuid())
  merchantId               String
  externalOrderId          String?
  orderDetails             Json?
  subtotal                 Decimal       @db.Decimal(12, 2)
  taxAmount                Decimal       @db.Decimal(12, 2)
  totalAmount              Decimal       @db.Decimal(12, 2)
  orderDate                DateTime
  associatedJournalEntryId String?       @unique
  createdAt                DateTime      @default(now())
  journalEntry             JournalEntry? @relation("SalesOrderJE", fields: [associatedJournalEntryId], references: [id])
  merchant                 Merchant      @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([orderDate])
}

model TaxRate {
  id          String   @id @default(cuid())
  merchantId  String
  name        String
  rate        Decimal  @db.Decimal(5, 4)
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  merchant    Merchant @relation(fields: [merchantId], references: [id])

  @@unique([merchantId, name])
  @@index([merchantId])
}

model Asset {
  id                               String             @id @default(cuid())
  merchantId                       String
  name                             String
  description                      String?
  purchaseDate                     DateTime
  purchaseCost                     Decimal            @db.Decimal(12, 2)
  depreciationMethod               DepreciationMethod @default(None)
  usefulLifeMonths                 Int?
  salvageValue                     Decimal            @default(0) @db.Decimal(12, 2)
  linkedAssetAccountId             String
  linkedAccumDeprAccountId         String
  linkedDeprExpenseAccountId       String
  lastDepreciationDate             DateTime?
  status                           AssetStatus        @default(Active)
  disposalDate                     DateTime?
  disposalProceeds                 Decimal?           @db.Decimal(12, 2)
  associatedPurchaseJournalEntryId String?
  associatedDisposalJournalEntryId String?            @unique
  imageUrl                         String?
  createdAt                        DateTime           @default(now())
  updatedAt                        DateTime           @updatedAt
  accumulatedDepreciation          Decimal?           @db.Decimal(12, 2)
  assetCategory                    String?
  assetType                        String?
  barcode                          String?
  condition                        String?
  costCenter                       String?
  custodianId                      String?
  customAssetTag                   String?
  department                       String?
  dimensions                       String?
  disposalMethod                   String?
  disposalReason                   String?
  documentUrls                     String[]           @default([])
  installationDate                 DateTime?
  insuranceDetails                 Json?
  isLeased                         Boolean            @default(false)
  lastInventoryDate                DateTime?
  lastMaintenanceDate              DateTime?
  leaseDetails                     String?
  leaseEndDate                     DateTime?
  leaseStartDate                   DateTime?
  location                         String?
  maintenanceHistory               Json?
  maintenanceSchedule              String?
  modelNumber                      String?
  netBookValue                     Decimal?           @db.Decimal(12, 2)
  nextMaintenanceDate              DateTime?
  parentAssetId                    String?
  purchaseOrderNumber              String?
  serialNumber                     String?
  specifications                   Json?
  vendorId                         String?
  warrantyDetails                  String?
  warrantyExpiryDate               DateTime?
  weight                           String?
  disposalJournalEntry             JournalEntry?      @relation("AssetDispJE", fields: [associatedDisposalJournalEntryId], references: [id])
  custodian                        User?              @relation("AssetCustodian", fields: [custodianId], references: [id])
  accumDeprAccount                 ChartOfAccount     @relation("AccumDeprAccount", fields: [linkedAccumDeprAccountId], references: [id])
  assetAccount                     ChartOfAccount     @relation("AssetAccount", fields: [linkedAssetAccountId], references: [id])
  deprExpenseAccount               ChartOfAccount     @relation("DeprExpenseAccount", fields: [linkedDeprExpenseAccountId], references: [id])
  merchant                         Merchant           @relation(fields: [merchantId], references: [id])
  parentAsset                      Asset?             @relation("AssetToChildAssets", fields: [parentAssetId], references: [id])
  childAssets                      Asset[]            @relation("AssetToChildAssets")
  vendor                           Vendor?            @relation(fields: [vendorId], references: [id])
  depreciationJournalEntries       JournalEntry[]     @relation("AssetDeprJE")

  @@index([merchantId])
  @@index([status])
  @@index([vendorId])
  @@index([custodianId])
  @@index([parentAssetId])
  @@index([serialNumber])
  @@index([barcode])
  @@index([customAssetTag])
}

model BankAccount {
  id                  String               @id @default(cuid())
  merchantId          String
  accountName         String
  bankName            String?
  accountNumberMasked String?
  currency            String               @default("USD")
  chartOfAccountId    String               @unique
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  metadata            String?
  chartOfAccount      ChartOfAccount       @relation(fields: [chartOfAccountId], references: [id])
  merchant            Merchant             @relation(fields: [merchantId], references: [id])
  reconciliations     BankReconciliation[]
  bankTransactions    BankTransaction[]

  @@index([merchantId])
}

model BankTransaction {
  id                        String                @id @default(cuid())
  bankAccountId             String
  transactionDate           DateTime
  description               String
  amount                    Decimal               @db.Decimal(12, 2)
  type                      String?
  status                    BankTransactionStatus @default(Unreconciled)
  matchedJournalEntryLineId String?
  createdAt                 DateTime              @default(now())
  exchangeRate              Decimal?              @db.Decimal(12, 6)
  externalId                String?
  foreignAmount             Decimal?              @db.Decimal(12, 2)
  foreignCurrency           String?
  updatedAt                 DateTime              @updatedAt
  bankAccount               BankAccount           @relation(fields: [bankAccountId], references: [id])

  @@index([bankAccountId])
  @@index([transactionDate])
  @@index([status])
  @@index([externalId])
}

model Employee {
  id                    String           @id @default(cuid())
  merchantId            String
  employeeName          String
  employmentStatus      EmploymentStatus @default(Active)
  basicPayRate          Decimal?         @db.Decimal(10, 2)
  payFrequency          String?
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  address               String?
  bankAccountNumber     String?
  bankName              String?
  bankRoutingNumber     String?
  bonusEligible         Boolean?         @default(false)
  certifications        Json?
  city                  String?
  commissionRate        Decimal?         @db.Decimal(5, 2)
  country               String?
  customFields          Json?
  dateOfBirth           DateTime?
  department            String?
  documentsUrls         String[]         @default([])
  education             Json?
  email                 String?
  emergencyContactName  String?
  emergencyContactPhone String?
  employeeId            String?
  employmentType        String?
  firstName             String?
  gender                String?
  healthInsurance       Boolean?         @default(false)
  hireDate              DateTime?
  imageUrl              String?
  jobTitle              String?
  lastName              String?
  lastReviewDate        DateTime?
  maritalStatus         String?
  middleName            String?
  mobilePhone           String?
  nationality           String?
  notes                 String?
  overtimeRate          Decimal?         @db.Decimal(10, 2)
  performanceRating     Int?
  phone                 String?
  photoUrl              String?
  probationEndDate      DateTime?
  resumeUrl             String?
  retirementPlan        Boolean?         @default(false)
  sickDays              Int?
  skills                String[]         @default([])
  state                 String?
  supervisor            String?
  taxId                 String?
  taxWithholding        String?
  terminationDate       DateTime?
  terminationReason     String?
  vacationDays          Int?
  zip                   String?
  merchant              Merchant         @relation(fields: [merchantId], references: [id])
  payrollDetails        PayrollDetail[]

  @@index([merchantId])
  @@index([employmentStatus])
  @@index([department])
  @@index([jobTitle])
  @@index([hireDate])
}

model PayrollRun {
  id                         String           @id @default(cuid())
  merchantId                 String
  payPeriodStartDate         DateTime
  payPeriodEndDate           DateTime
  paymentDate                DateTime
  totalGrossWages            Decimal          @db.Decimal(12, 2)
  totalEmployeeTaxesWithheld Decimal          @db.Decimal(12, 2)
  totalEmployerTaxes         Decimal          @db.Decimal(12, 2)
  totalNetPay                Decimal          @db.Decimal(12, 2)
  status                     PayrollRunStatus @default(Draft)
  associatedJournalEntryId   String?          @unique
  createdAt                  DateTime         @default(now())
  updatedAt                  DateTime         @updatedAt
  payrollDetails             PayrollDetail[]
  journalEntry               JournalEntry?    @relation("PayrollRunJE", fields: [associatedJournalEntryId], references: [id])
  merchant                   Merchant         @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([paymentDate])
  @@index([status])
}

model Customer {
  id                     String              @id @default(cuid())
  merchantId             String
  name                   String
  email                  String
  phone                  String?
  address                String?
  city                   String?
  state                  String?
  zip                    String?
  country                String?
  taxId                  String?
  notes                  String?
  status                 String              @default("active")
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt
  alternateEmail         String?
  anniversaryDate        DateTime?
  birthDate              DateTime?
  companyName            String?
  contactPerson          String?
  creditLimit            Decimal?            @db.Decimal(12, 2)
  currency               String?             @default("USD")
  currentBalance         Decimal?            @db.Decimal(12, 2)
  customFields           Json?
  customerCategory       String?
  customerType           String?
  fax                    String?
  imageUrl               String?
  industry               String?
  lastPurchaseDate       DateTime?
  mobilePhone            String?
  onboardingDate         DateTime?
  paymentTerms           String?
  preferredPaymentMethod String?
  referralSource         String?
  shippingAddress        String?
  shippingCity           String?
  shippingCountry        String?
  shippingState          String?
  shippingZip            String?
  size                   String?
  tags                   String[]            @default([])
  vatNumber              String?
  website                String?
  collectionCases        CollectionCase[]
  creditNotes            CreditNote[]
  merchant               Merchant            @relation(fields: [merchantId], references: [id])
  customerCredits        CustomerCredit[]
  customerStatements     CustomerStatement[]
  invoices               Invoice[]
  invoiceTemplates       InvoiceTemplate[]
  recurringInvoices      RecurringInvoice[]

  @@index([merchantId])
  @@index([email])
  @@index([status])
  @@index([customerCategory])
  @@index([lastPurchaseDate])
}

model Invoice {
  id                       String              @id @default(cuid())
  merchantId               String
  customerId               String
  invoiceNumber            String
  issueDate                DateTime
  dueDate                  DateTime
  subtotal                 Decimal             @db.Decimal(12, 2)
  taxAmount                Decimal             @db.Decimal(12, 2)
  totalAmount              Decimal             @db.Decimal(12, 2)
  amountDue                Decimal             @db.Decimal(12, 2)
  status                   InvoiceStatus       @default(Draft)
  notes                    String?
  terms                    String?
  associatedJournalEntryId String?             @unique
  createdAt                DateTime            @default(now())
  updatedAt                DateTime            @updatedAt
  documentUrls             String[]            @default([])
  imageUrl                 String?
  revenueAccountId         String?
  creditApplications       CreditApplication[]
  journalEntry             JournalEntry?       @relation("InvoiceJE", fields: [associatedJournalEntryId], references: [id])
  customer                 Customer            @relation(fields: [customerId], references: [id])
  merchant                 Merchant            @relation(fields: [merchantId], references: [id])
  revenueAccount           ChartOfAccount?     @relation("InvoiceRevenueAccount", fields: [revenueAccountId], references: [id])
  items                    InvoiceItem[]
  payments                 InvoicePayment[]
  paymentReminders         PaymentReminder[]

  @@unique([merchantId, invoiceNumber])
  @@index([merchantId])
  @@index([customerId])
  @@index([status])
  @@index([dueDate])
  @@index([revenueAccountId])
}

model InvoiceItem {
  id               String          @id @default(cuid())
  invoiceId        String
  description      String
  quantity         Decimal         @db.Decimal(10, 2)
  unitPrice        Decimal         @db.Decimal(12, 2)
  taxRateId        String?
  taxRate          Decimal?        @db.Decimal(5, 4)
  taxAmount        Decimal         @db.Decimal(12, 2)
  lineTotal        Decimal         @db.Decimal(12, 2)
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  revenueAccountId String?
  invoice          Invoice         @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  revenueAccount   ChartOfAccount? @relation("InvoiceItemRevenueAccount", fields: [revenueAccountId], references: [id])

  @@index([invoiceId])
  @@index([revenueAccountId])
}

model InvoicePayment {
  id                       String        @id @default(cuid())
  invoiceId                String
  paymentDate              DateTime
  amountPaid               Decimal       @db.Decimal(12, 2)
  paymentMethod            String
  referenceNumber          String?
  associatedJournalEntryId String?       @unique
  createdAt                DateTime      @default(now())
  journalEntry             JournalEntry? @relation("InvoicePaymentJE", fields: [associatedJournalEntryId], references: [id])
  invoice                  Invoice       @relation(fields: [invoiceId], references: [id])

  @@index([invoiceId])
}

model RecurringInvoice {
  id                String                 @id @default(cuid())
  merchantId        String
  customerId        String
  name              String
  frequency         RecurringFrequency
  interval          Int                    @default(1)
  startDate         DateTime
  endDate           DateTime?
  nextDate          DateTime
  daysDueAfter      Int
  status            RecurringStatus        @default(Active)
  lastGeneratedDate DateTime?
  subtotal          Decimal                @db.Decimal(12, 2)
  taxAmount         Decimal                @db.Decimal(12, 2)
  totalAmount       Decimal                @db.Decimal(12, 2)
  notes             String?
  terms             String?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  customer          Customer               @relation(fields: [customerId], references: [id])
  merchant          Merchant               @relation(fields: [merchantId], references: [id])
  items             RecurringInvoiceItem[]

  @@index([merchantId])
  @@index([customerId])
  @@index([status])
  @@index([nextDate])
}

model RecurringInvoiceItem {
  id                 String           @id @default(cuid())
  recurringInvoiceId String
  description        String
  quantity           Decimal          @db.Decimal(10, 2)
  unitPrice          Decimal          @db.Decimal(12, 2)
  taxRateId          String?
  taxRate            Decimal?         @db.Decimal(5, 4)
  taxAmount          Decimal          @db.Decimal(12, 2)
  lineTotal          Decimal          @db.Decimal(12, 2)
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  recurringInvoice   RecurringInvoice @relation(fields: [recurringInvoiceId], references: [id], onDelete: Cascade)

  @@index([recurringInvoiceId])
}

model InvoiceTemplate {
  id           String                @id @default(cuid())
  merchantId   String
  name         String
  description  String?
  customerId   String?
  daysDueAfter Int
  subtotal     Decimal               @db.Decimal(12, 2)
  taxAmount    Decimal               @db.Decimal(12, 2)
  totalAmount  Decimal               @db.Decimal(12, 2)
  notes        String?
  terms        String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  customer     Customer?             @relation(fields: [customerId], references: [id])
  merchant     Merchant              @relation(fields: [merchantId], references: [id])
  items        InvoiceTemplateItem[]

  @@index([merchantId])
  @@index([customerId])
}

model InvoiceTemplateItem {
  id              String          @id @default(cuid())
  templateId      String
  description     String
  quantity        Decimal         @db.Decimal(10, 2)
  unitPrice       Decimal         @db.Decimal(12, 2)
  taxRateId       String?
  taxRate         Decimal?        @db.Decimal(5, 4)
  taxAmount       Decimal         @db.Decimal(12, 2)
  lineTotal       Decimal         @db.Decimal(12, 2)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  invoiceTemplate InvoiceTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId])
}

model CreditNote {
  id                 String              @id @default(cuid())
  merchantId         String
  customerId         String
  creditNoteNumber   String
  referenceInvoiceId String?
  issueDate          DateTime
  status             CreditNoteStatus    @default(Draft)
  type               CreditNoteType
  subtotal           Decimal             @db.Decimal(12, 2)
  taxAmount          Decimal             @db.Decimal(12, 2)
  totalAmount        Decimal             @db.Decimal(12, 2)
  remainingCredit    Decimal             @db.Decimal(12, 2)
  notes              String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  applications       CreditApplication[]
  customer           Customer            @relation(fields: [customerId], references: [id])
  merchant           Merchant            @relation(fields: [merchantId], references: [id])
  items              CreditNoteItem[]

  @@unique([merchantId, creditNoteNumber])
  @@index([merchantId])
  @@index([customerId])
  @@index([status])
}

model CreditNoteItem {
  id           String     @id @default(cuid())
  creditNoteId String
  description  String
  quantity     Decimal    @db.Decimal(10, 2)
  unitPrice    Decimal    @db.Decimal(12, 2)
  taxRateId    String?
  taxRate      Decimal?   @db.Decimal(5, 4)
  taxAmount    Decimal    @db.Decimal(12, 2)
  lineTotal    Decimal    @db.Decimal(12, 2)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  creditNote   CreditNote @relation(fields: [creditNoteId], references: [id], onDelete: Cascade)

  @@index([creditNoteId])
}

model CreditApplication {
  id              String     @id @default(cuid())
  creditNoteId    String
  invoiceId       String
  amountApplied   Decimal    @db.Decimal(12, 2)
  applicationDate DateTime
  createdAt       DateTime   @default(now())
  creditNote      CreditNote @relation(fields: [creditNoteId], references: [id])
  invoice         Invoice    @relation(fields: [invoiceId], references: [id])

  @@index([creditNoteId])
  @@index([invoiceId])
}

model CustomerCredit {
  id              String    @id @default(cuid())
  customerId      String
  amount          Decimal   @db.Decimal(12, 2)
  description     String
  issueDate       DateTime
  expiryDate      DateTime?
  remainingAmount Decimal   @db.Decimal(12, 2)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  customer        Customer  @relation(fields: [customerId], references: [id])

  @@index([customerId])
}

model PaymentReminder {
  id              String                @id @default(cuid())
  invoiceId       String
  status          PaymentReminderStatus @default(Scheduled)
  emailTemplateId String?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  merchantId      String
  message         String?
  scheduledDate   DateTime
  sentAt          DateTime?
  emailTemplate   EmailTemplate?        @relation(fields: [emailTemplateId], references: [id])
  invoice         Invoice               @relation(fields: [invoiceId], references: [id])
  merchant        Merchant              @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([invoiceId])
  @@index([scheduledDate])
  @@index([status])
}

model CustomerStatement {
  id             String          @id @default(cuid())
  customerId     String
  merchantId     String
  startDate      DateTime
  endDate        DateTime
  openingBalance Decimal         @db.Decimal(12, 2)
  closingBalance Decimal         @db.Decimal(12, 2)
  totalInvoiced  Decimal         @db.Decimal(12, 2)
  totalPaid      Decimal         @db.Decimal(12, 2)
  totalCredits   Decimal         @db.Decimal(12, 2)
  generatedAt    DateTime
  customer       Customer        @relation(fields: [customerId], references: [id])
  merchant       Merchant        @relation(fields: [merchantId], references: [id])
  items          StatementItem[]

  @@index([customerId])
  @@index([merchantId])
}

model StatementItem {
  id              String            @id @default(cuid())
  statementId     String
  date            DateTime
  description     String
  type            String
  referenceId     String
  referenceNumber String
  amount          Decimal           @db.Decimal(12, 2)
  runningBalance  Decimal           @db.Decimal(12, 2)
  statement       CustomerStatement @relation(fields: [statementId], references: [id], onDelete: Cascade)

  @@index([statementId])
}

model EmailTemplate {
  id               String            @id @default(cuid())
  merchantId       String
  name             String
  subject          String
  body             String
  isDefault        Boolean           @default(false)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  merchant         Merchant          @relation(fields: [merchantId], references: [id])
  paymentReminders PaymentReminder[]

  @@index([merchantId])
}

model EmailLog {
  id             String      @id @default(cuid())
  merchantId     String
  invoiceId      String?
  customerId     String?
  recipientEmail String
  subject        String
  body           String
  status         EmailStatus
  errorMessage   String?
  sentAt         DateTime?
  createdAt      DateTime    @default(now())
  merchant       Merchant    @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([invoiceId])
  @@index([customerId])
  @@index([status])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserPreferences {
  id                String   @id @default(cuid())
  userId            String   @unique
  theme             String   @default("system")
  dateFormat        String?
  timeFormat        String?
  language          String?
  startPage         String?
  notifications     Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  currencyFormat    String?
  displayDensity    String?  @default("comfortable")
  numberFormat      String?
  tablePageSize     Int?     @default(10)
  defaultMerchantId String?
  bio               String?
  department        String?
  jobTitle          String?
  phone             String?
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Subscription {
  id                   String             @id @default(cuid())
  merchantId           String             @unique
  stripeSubscriptionId String?
  plan                 SubscriptionPlan   @default(Free)
  status               SubscriptionStatus @default(Active)
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean            @default(false)
  trialEnd             DateTime?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  merchant             Merchant           @relation(fields: [merchantId], references: [id], onDelete: Cascade)
  usageRecords         UsageRecord[]
}

model UsageRecord {
  id               String       @id @default(cuid())
  subscriptionId   String
  month            Int
  year             Int
  transactionCount Int          @default(0)
  apiCallCount     Int          @default(0)
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  subscription     Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@unique([subscriptionId, month, year])
}

model CashFlowItem {
  id            String             @id @default(cuid())
  merchantId    String
  description   String
  amount        Decimal            @db.Decimal(12, 2)
  type          CashFlowItemType
  expectedDate  DateTime
  status        CashFlowItemStatus
  category      String?
  referenceType String?
  referenceId   String?
  notes         String?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  accountId     String?
  account       ChartOfAccount?    @relation("CashFlowAccount", fields: [accountId], references: [id])
  merchant      Merchant           @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([type])
  @@index([status])
  @@index([expectedDate])
  @@index([category])
  @@index([accountId])
}

model RecurringCashFlowItem {
  id          String             @id @default(cuid())
  merchantId  String
  description String
  amount      Decimal            @db.Decimal(12, 2)
  type        CashFlowItemType
  frequency   RecurringFrequency
  startDate   DateTime
  endDate     DateTime?
  nextDate    DateTime
  status      RecurringStatus    @default(Active)
  category    String?
  notes       String?
  isActive    Boolean            @default(true)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  merchant    Merchant           @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([type])
  @@index([frequency])
  @@index([nextDate])
  @@index([isActive])
}

model CashFlowCategory {
  id          String           @id @default(cuid())
  merchantId  String
  name        String
  description String?
  type        CashFlowItemType
  parentId    String?
  color       String
  isActive    Boolean          @default(true)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  merchant    Merchant         @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([type])
  @@index([isActive])
}

model InventoryItem {
  id                 String                 @id @default(cuid())
  merchantId         String
  name               String
  description        String?
  sku                String
  category           String?
  unitOfMeasure      String
  unitCost           Decimal                @db.Decimal(12, 2)
  quantityOnHand     Decimal                @db.Decimal(12, 2)
  reorderPoint       Decimal?               @db.Decimal(12, 2)
  reorderQuantity    Decimal?               @db.Decimal(12, 2)
  vendorId           String?
  assetAccountId     String
  imageUrl           String?
  status             InventoryItemStatus    @default(Active)
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  additionalImages   String[]               @default([])
  alternateVendorIds String[]               @default([])
  assemblyComponents Json?
  barcode            String?
  binLocation        String?
  brand              String?
  cogsAccountId      String?
  color              String?
  customFields       Json?
  dimensions         String?
  expiryDate         DateTime?
  isAssembly         Boolean                @default(false)
  leadTimeInDays     Int?
  lotNumber          String?
  maximumOrderQty    Decimal?               @db.Decimal(12, 2)
  minimumOrderQty    Decimal?               @db.Decimal(12, 2)
  model              String?
  notes              String?
  preferredVendorId  String?
  retailPrice        Decimal?               @db.Decimal(12, 2)
  revenueAccountId   String?
  serialNumber       String?
  shelfLife          Int?
  size               String?
  subcategory        String?
  taxRateId          String?
  taxable            Boolean                @default(true)
  vendorItemCode     String?
  warehouseLocation  String?
  weight             Decimal?               @db.Decimal(10, 3)
  weightUnit         String?
  wholesalePrice     Decimal?               @db.Decimal(12, 2)
  merchant           Merchant               @relation(fields: [merchantId], references: [id])
  transactions       InventoryTransaction[]

  @@index([merchantId])
  @@index([sku])
  @@index([barcode])
  @@index([status])
  @@index([vendorId])
  @@index([preferredVendorId])
  @@index([category, subcategory])
}

model InventoryTransaction {
  id              String                   @id @default(cuid())
  merchantId      String
  inventoryItemId String
  transactionDate DateTime
  transactionType InventoryTransactionType
  quantity        Decimal                  @db.Decimal(12, 2)
  unitCost        Decimal?                 @db.Decimal(12, 2)
  totalCost       Decimal                  @db.Decimal(12, 2)
  referenceType   String?
  referenceId     String?
  notes           String?
  createdAt       DateTime                 @default(now())
  updatedAt       DateTime                 @updatedAt
  inventoryItem   InventoryItem            @relation(fields: [inventoryItemId], references: [id])
  merchant        Merchant                 @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([inventoryItemId])
  @@index([transactionDate])
  @@index([transactionType])
}

model BudgetItem {
  id         String   @id @default(cuid())
  merchantId String
  accountId  String
  year       Int
  month      Int
  amount     Decimal  @db.Decimal(12, 2)
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  merchant   Merchant @relation(fields: [merchantId], references: [id])

  @@unique([merchantId, accountId, year, month])
  @@index([merchantId])
  @@index([accountId])
  @@index([year, month])
}

model BudgetTemplate {
  id          String               @id @default(cuid())
  merchantId  String
  name        String
  description String?
  isDefault   Boolean              @default(false)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  merchant    Merchant             @relation(fields: [merchantId], references: [id])
  items       BudgetTemplateItem[]

  @@index([merchantId])
  @@index([isDefault])
}

model BudgetTemplateItem {
  id         String         @id @default(cuid())
  templateId String
  accountId  String
  amount     Decimal        @db.Decimal(12, 2)
  notes      String?
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  template   BudgetTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, accountId])
  @@index([templateId])
  @@index([accountId])
}

model BankReconciliation {
  id                          String                   @id @default(cuid())
  merchantId                  String
  bankAccountId               String
  statementEndDate            DateTime
  statementOpeningBalanceBase Decimal                  @db.Decimal(12, 2)
  statementEndingBalanceBase  Decimal                  @db.Decimal(12, 2)
  calculatedBookBalanceBase   Decimal                  @db.Decimal(12, 2)
  status                      BankReconciliationStatus @default(InProgress)
  reconciledByUserId          String?
  reconciledAt                DateTime?
  createdAt                   DateTime                 @default(now())
  updatedAt                   DateTime                 @updatedAt
  bankAccount                 BankAccount              @relation(fields: [bankAccountId], references: [id])
  merchant                    Merchant                 @relation(fields: [merchantId], references: [id])
  matches                     BankTransactionMatch[]

  @@index([merchantId])
  @@index([bankAccountId])
  @@index([status])
}

model BankTransactionMatch {
  id                   String             @id @default(cuid())
  bankReconciliationId String
  bankTransactionId    String
  journalEntryLineId   String
  matchedAt            DateTime
  matchedByUserId      String?
  bankReconciliation   BankReconciliation @relation(fields: [bankReconciliationId], references: [id])

  @@index([bankReconciliationId])
  @@index([bankTransactionId])
}

model Expense {
  id               String        @id @default(cuid())
  merchantId       String
  vendorId         String
  date             DateTime
  amount           Decimal       @db.Decimal(12, 2)
  description      String
  category         String
  paymentMethod    String
  status           ExpenseStatus @default(Pending)
  receiptUrl       String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  documentUrls     String[]      @default([])
  expenseAccountId String?
  imageUrls        String[]      @default([])
  notes            String?
  merchant         Merchant      @relation(fields: [merchantId], references: [id])
  vendor           Vendor        @relation(fields: [vendorId], references: [id])

  @@index([merchantId])
  @@index([vendorId])
  @@index([status])
  @@index([date])
  @@index([category])
  @@index([expenseAccountId])
}

model PayrollDetail {
  id               String     @id @default(cuid())
  merchantId       String
  payrollRunId     String
  employeeId       String
  hoursWorked      Decimal?   @db.Decimal(10, 2)
  regularPay       Decimal    @db.Decimal(12, 2)
  overtimePay      Decimal    @db.Decimal(12, 2)
  bonusPay         Decimal    @db.Decimal(12, 2)
  grossPay         Decimal    @db.Decimal(12, 2)
  federalTax       Decimal    @db.Decimal(12, 2)
  stateTax         Decimal    @db.Decimal(12, 2)
  ficaTax          Decimal    @db.Decimal(12, 2)
  otherDeductions  Decimal    @db.Decimal(12, 2)
  netPay           Decimal    @db.Decimal(12, 2)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
  expenseAccountId String?
  employee         Employee   @relation(fields: [employeeId], references: [id])
  merchant         Merchant   @relation(fields: [merchantId], references: [id])
  payrollRun       PayrollRun @relation(fields: [payrollRunId], references: [id])

  @@index([merchantId])
  @@index([payrollRunId])
  @@index([employeeId])
  @@index([expenseAccountId])
}

model TaxReport {
  id                 String        @id @default(cuid())
  merchantId         String
  reportType         TaxReportType
  startDate          DateTime
  endDate            DateTime
  totalTaxableAmount Decimal       @db.Decimal(12, 2)
  totalTaxAmount     Decimal       @db.Decimal(12, 2)
  taxRateBreakdown   Json
  generatedAt        DateTime
  merchant           Merchant      @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([reportType])
  @@index([startDate, endDate])
}

model CollectionCase {
  id              String               @id @default(cuid())
  merchantId      String
  customerId      String
  assignedTo      String
  status          CollectionCaseStatus @default(New)
  priority        String
  totalAmount     Decimal              @db.Decimal(12, 2)
  amountCollected Decimal              @db.Decimal(12, 2)
  amountRemaining Decimal              @db.Decimal(12, 2)
  dueDate         DateTime
  description     String
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt
  activities      CollectionActivity[]
  customer        Customer             @relation(fields: [customerId], references: [id])
  merchant        Merchant             @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([customerId])
  @@index([status])
  @@index([priority])
}

model CollectionActivity {
  id           String                 @id @default(cuid())
  caseId       String
  type         CollectionActivityType
  date         DateTime
  notes        String
  outcome      String
  followUpDate DateTime?
  createdBy    String
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  case         CollectionCase         @relation(fields: [caseId], references: [id])

  @@index([caseId])
  @@index([type])
  @@index([date])
}

model CollectionTemplate {
  id          String                   @id @default(cuid())
  merchantId  String
  name        String
  description String
  createdAt   DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt
  merchant    Merchant                 @relation(fields: [merchantId], references: [id])
  steps       CollectionTemplateStep[]

  @@index([merchantId])
}

model CollectionTemplateStep {
  id               String                 @id @default(cuid())
  templateId       String
  stepNumber       Int
  daysAfterDue     Int
  activityType     CollectionActivityType
  description      String
  emailTemplateId  String?
  letterTemplateId String?
  template         CollectionTemplate     @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId])
}

model ApiKey {
  id         String    @id @default(cuid())
  merchantId String
  name       String
  prefix     String
  key        String?
  expiresAt  DateTime?
  lastUsed   DateTime?
  scopes     String[]
  isActive   Boolean   @default(true)
  createdAt  DateTime  @default(now())
  merchant   Merchant  @relation(fields: [merchantId], references: [id])

  @@index([merchantId])
  @@index([prefix])
  @@index([isActive])
}

model UserMerchantPermission {
  id                String          @id @default(cuid())
  userId            String
  merchantId        String
  permissionLevel   PermissionLevel @default(Staff)
  customPermissions Json?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  merchant          Merchant        @relation(fields: [merchantId], references: [id], onDelete: Cascade)
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, merchantId])
  @@index([userId])
  @@index([merchantId])
}

model AuditLog {
  id         String    @id @default(cuid())
  merchantId String?
  userId     String?
  action     String
  resource   String
  resourceId String?
  details    Json?
  ipAddress  String?
  userAgent  String?
  path       String
  method     String
  statusCode Int
  timestamp  DateTime  @default(now())
  Merchant   Merchant? @relation(fields: [merchantId], references: [id])
  User       User?     @relation(fields: [userId], references: [id])

  @@index([action])
  @@index([merchantId])
  @@index([path])
  @@index([resource])
  @@index([timestamp])
  @@index([userId])
}

// Restaurant Management Models
model Restaurant {
  id          String   @id @default(cuid())
  merchantId  String
  name        String
  description String?
  address     String?
  phone       String?
  email       String?
  website     String?
  cuisineType String?
  logoUrl     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  merchant    Merchant       @relation(fields: [merchantId], references: [id], onDelete: Cascade)
  menus       Menu[]
  tables      RestaurantTable[]
  staff       RestaurantStaff[]
  orders      RestaurantOrder[]

  @@index([merchantId])
  @@index([isActive])
}

model Menu {
  id           String   @id @default(cuid())
  restaurantId String
  name         String
  description  String?
  isActive     Boolean  @default(true)
  isPublished  Boolean  @default(false)
  displayOrder Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  restaurant   Restaurant     @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  categories   MenuCategory[]
  items        MenuItem[]

  @@index([restaurantId])
  @@index([isActive])
  @@index([isPublished])
}

model MenuCategory {
  id           String   @id @default(cuid())
  menuId       String
  name         String
  description  String?
  displayOrder Int      @default(0)
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  menu         Menu       @relation(fields: [menuId], references: [id], onDelete: Cascade)
  items        MenuItem[]

  @@index([menuId])
  @@index([isActive])
  @@index([displayOrder])
}

model MenuItem {
  id           String   @id @default(cuid())
  menuId       String
  categoryId   String?
  name         String
  description  String?
  price        Decimal  @db.Decimal(10, 2)
  imageUrl     String?
  isAvailable  Boolean  @default(true)
  isActive     Boolean  @default(true)
  displayOrder Int      @default(0)
  allergens    String[] @default([])
  tags         String[] @default([])
  preparationTime Int?  // in minutes
  calories     Int?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  menu         Menu          @relation(fields: [menuId], references: [id], onDelete: Cascade)
  category     MenuCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  orderItems   RestaurantOrderItem[]

  @@index([menuId])
  @@index([categoryId])
  @@index([isAvailable])
  @@index([isActive])
  @@index([displayOrder])
}

model RestaurantTable {
  id           String   @id @default(cuid())
  restaurantId String
  tableNumber  String
  capacity     Int
  location     String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  orders       RestaurantOrder[]

  @@unique([restaurantId, tableNumber])
  @@index([restaurantId])
  @@index([isActive])
}

model RestaurantStaff {
  id           String   @id @default(cuid())
  restaurantId String
  userId       String?
  name         String
  email        String?
  phone        String?
  role         RestaurantStaffRole
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  user         User?      @relation(fields: [userId], references: [id], onDelete: SetNull)
  orders       RestaurantOrder[]

  @@index([restaurantId])
  @@index([userId])
  @@index([role])
  @@index([isActive])
}

model RestaurantOrder {
  id           String   @id @default(cuid())
  restaurantId String
  tableId      String?
  staffId      String?
  orderNumber  String
  customerName String?
  customerPhone String?
  status       RestaurantOrderStatus @default(Pending)
  totalAmount  Decimal  @db.Decimal(10, 2)
  notes        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  restaurant   Restaurant @relation(fields: [restaurantId], references: [id], onDelete: Cascade)
  table        RestaurantTable? @relation(fields: [tableId], references: [id], onDelete: SetNull)
  staff        RestaurantStaff? @relation(fields: [staffId], references: [id], onDelete: SetNull)
  items        RestaurantOrderItem[]

  @@unique([restaurantId, orderNumber])
  @@index([restaurantId])
  @@index([tableId])
  @@index([staffId])
  @@index([status])
  @@index([createdAt])
}

model RestaurantOrderItem {
  id         String   @id @default(cuid())
  orderId    String
  menuItemId String
  quantity   Int
  unitPrice  Decimal  @db.Decimal(10, 2)
  totalPrice Decimal  @db.Decimal(10, 2)
  notes      String?
  createdAt  DateTime @default(now())

  // Relations
  order      RestaurantOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItem   MenuItem        @relation(fields: [menuItemId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([menuItemId])
}

enum RestaurantStaffRole {
  Manager
  Chef
  Waiter
  Cashier
  Host
}

enum RestaurantOrderStatus {
  Pending
  Confirmed
  Preparing
  Ready
  Served
  Completed
  Cancelled
}

enum SubscriptionPlan {
  Free
  Standard
  Professional
  Enterprise
}

enum SubscriptionStatus {
  Active
  PastDue
  Canceled
  Trialing
  Incomplete
}

enum AccountType {
  Asset
  Liability
  Equity
  Revenue
  Expense
}

enum JournalEntryLineType {
  Debit
  Credit
}

enum JournalEntrySourceType {
  Manual
  SalesOrder
  Bill
  BillPayment
  Depreciation
  AssetPurchase
  AssetDisposal
  PayrollRun
  BankAdjustment
  Invoice
  InvoicePayment
  CreditNote
}

enum BillStatus {
  Draft
  Open
  Paid
  Void
}

enum AssetStatus {
  Active
  Disposed
  Sold
}

enum ExpenseStatus {
  Pending
  Approved
  Rejected
  Paid
}

enum DepreciationMethod {
  StraightLine
  None
}

enum BankTransactionStatus {
  Unreconciled
  Reconciled
  Adjusted
}

enum EmploymentStatus {
  Active
  Inactive
}

enum PayrollRunStatus {
  Draft
  Processed
  Archived
}

enum InvoiceStatus {
  Draft
  Sent
  PartiallyPaid
  Paid
  Overdue
  Void
}

enum RecurringFrequency {
  Weekly
  Monthly
  Quarterly
  Biannually
  Annually
  Custom
}

enum RecurringStatus {
  Active
  Paused
  Completed
}

enum EmailStatus {
  Sent
  Failed
  Scheduled
}

enum CreditNoteStatus {
  Draft
  Open
  Closed
  Void
}

enum CreditNoteType {
  CustomerRefund
  SupplierRefund
  WriteOff
  ReturnCredit
  Adjustment
}

enum PaymentReminderStatus {
  Scheduled
  Sent
  Cancelled
}

enum BankReconciliationStatus {
  InProgress
  Completed
}

enum InventoryItemStatus {
  Active
  Inactive
}

enum InventoryTransactionType {
  Purchase
  Sale
  Adjustment
  Transfer
  Waste
}

enum CashFlowItemType {
  Inflow
  Outflow
}

enum CashFlowItemStatus {
  Projected
  Confirmed
  Completed
}

enum TaxReportType {
  Sales
  Income
  Payroll
}

enum CollectionCaseStatus {
  New
  InProgress
  OnHold
  Resolved
  Closed
}

enum CollectionActivityType {
  Call
  Email
  Letter
  Meeting
  Payment
  Note
}

enum PermissionLevel {
  Owner
  Admin
  Manager
  Staff
  ReadOnly
}
